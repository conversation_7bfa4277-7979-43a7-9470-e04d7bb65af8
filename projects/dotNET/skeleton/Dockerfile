FROM mcr.microsoft.com/dotnet/sdk:${{ values.dotnetVersion.substring(3) }} AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY *.csproj ./
RUN dotnet restore

# Copy everything else and build
COPY . ./
RUN dotnet publish -c Release -o /app/publish

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:${{ values.dotnetVersion.substring(3) }}
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "${{ values.projectName }}.dll"]
