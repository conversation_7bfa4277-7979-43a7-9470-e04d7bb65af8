# ${{ values.projectName }}

${{ values.description }}

## About

This is a .NET web application created from the ESET .NET template. It can be run locally or deployed as a Docker container.

## Development

### Prerequisites

- ${{ values.dotnetVersion }} SDK or later
- Docker (optional, for container builds)

### Run Locally

```bash
dotnet build
dotnet run
```

### Build for Production

```bash
dotnet publish -c Release
```

### Docker

#### Build Docker Image

```bash
docker build -t ${{ values.projectName | lower }}:latest .
```

#### Run Docker Container

```bash
docker run -p 8080:80 ${{ values.projectName | lower }}:latest
```

The application will be available at `http://localhost:8080`

## API Endpoints

- `/hello` - Returns a greeting message

## Static Content

Static content is served from the `wwwroot` directory.
