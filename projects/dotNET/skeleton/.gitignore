# .NET Core build folders
bin/
obj/

# Visual Studio / Visual Studio Code files
.vs/
.vscode/
*.user
*.userosscache
*.suo
*.userprefs
.idea/

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet Packages
*.nupkg
*.snupkg
**/[Pp]ackages/*
!**/[Pp]ackages/build/
*.nuget.props
*.nuget.targets

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# JetBrains Rider
.idea/
*.sln.iml