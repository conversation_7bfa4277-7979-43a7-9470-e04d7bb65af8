# Getting Started

This guide will help you set up and run the ${{ values.projectName }} application locally.

## Prerequisites

- ${{ values.dotnetVersion }} SDK or later
- Git
- IDE of your choice (Visual Studio, VS Code, JetBrains Rider, etc.)

## Clone the Repository

```bash
git clone https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}.git
cd ${{ (values.repoUrl | parseRepoUrl).repo }}
```

## Build and Run

### Build the Project

```bash
dotnet build
```

### Run the Application

```bash
dotnet run
```

By default, the application will be available at `http://localhost:5000`.

## Accessing the Application

Once the application is running, you can:

- Visit the homepage at `http://localhost:5000`
- Access the API endpoint at `http://localhost:5000/hello`

## Next Steps

After you've got the application running, you might want to:

- Explore the [Development Guide](development-guide.md) to understand how to extend the application
- Check the [API Reference](api-reference.md) for details on available endpoints
- Review the [Project Structure](project-structure.md) to understand the codebase organization
