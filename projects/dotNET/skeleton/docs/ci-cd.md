# CI/CD Pipeline

${{ values.projectName }} uses GitLab CI/CD for continuous integration and deployment.

## Pipeline Configuration

The pipeline is defined in the `.gitlab-ci.yml` file at the root of the repository. It includes stages for building the application, publishing documentation, and creating Docker images.

The pipeline uses GitLab CI/CD variables that are set at the project level during repository creation. These include Azure Container Registry credentials and Docker image configuration.

```yaml
stages:
  - build
  - publish
  - docker

variables:
  DOTNET_CLI_HOME: "/tmp/DOTNET_CLI_HOME"
  ENTITY_NAMESPACE: "${{ (values.repoUrl | parseRepoUrl).owner }}"
  ENTITY_KIND: "Component"
  ENTITY_NAME: ${{ values.component_id }}

default:
  image: mcr.microsoft.com/dotnet/sdk:${{ values.dotnetVersion.substring(3) }}

build:
  stage: build
  script:
    - echo "Building ${{ values.projectName }} with ${{ values.dotnetVersion }}..."
    - dotnet restore
    - dotnet build --configuration Release --no-restore
    - dotnet publish --configuration Release --no-build --output ./publish
  artifacts:
    name: "${{ values.projectName }}-app"
    paths:
      - ./publish/
    expire_in: 1 week

docker-build-push:
  stage: docker
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    # Extract version from csproj file
    - apk add --no-cache grep libxml2-utils git
    - VERSION=$(grep -oP '<Version>\K[^<]+' *.csproj)
    - COMMIT_SHA=$(git rev-parse --short=6 HEAD)
    - VERSION_TAG="$VERSION-$COMMIT_SHA"
    # Login to ACR
    - echo "$ACR_PASSWORD" | docker login $ACR_REGISTRY -u $ACR_USERNAME --password-stdin
  script:
    # Build and push Docker image
    - docker build -t $ACR_REGISTRY/$IMAGE_NAME:latest -t $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG .
    - docker push $ACR_REGISTRY/$IMAGE_NAME:latest
    - docker push $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG
  only:
    - main
```

## Pipeline Stages

### Build Stage

The build stage:

1. Restores NuGet packages
2. Builds the application in Release configuration
3. Publishes the application to the `./publish` directory
4. Creates an artifact containing the published application

### Docker Stage

The docker stage:

1. Extracts the version from the .csproj file
2. Generates a version tag by combining the version with the short commit SHA
3. Logs in to Azure Container Registry (ACR)
4. Builds a Docker image using the Dockerfile in the repository
5. Tags the image with both `latest` and the version tag
6. Pushes both tags to ACR

## Artifacts

The pipeline produces the following artifacts:

- **${{ values.projectName }}-app**: Contains the published application
  - Expiration: 1 week

## Docker Images

The pipeline produces the following Docker images in the Azure Container Registry:

- **${{ values.projectName | lower }}:latest**: The latest version of the application
- **${{ values.projectName | lower }}:[version]-[commit-sha]**: A specific version of the application with the commit SHA

## Pipeline Status

You can view the status of pipeline runs at:
[GitLab Pipelines](https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}/-/pipelines)

## Extending the Pipeline

To extend the pipeline with additional stages (e.g., test, deploy), modify the `.gitlab-ci.yml` file. For example:

```yaml
stages:
  - build
  - test
  - deploy

# ... existing build job ...

test:
  stage: test
  script:
    - dotnet test

deploy:
  stage: deploy
  script:
    - echo "Deploying application..."
    # Add deployment commands here
  only:
    - main
```
