# ${{ values.projectName }}

${{ values.description }}

## Overview

This is a .NET web application created using the ESET .NET template. The project is built with ${{ values.dotnetVersion }} and provides a simple web API and static content serving capabilities.

## Quick Links

- [Getting Started](getting-started.md)
- [Development Guide](development-guide.md)
- [API Reference](api-reference.md)
- [CI/CD Pipeline](ci-cd.md)
- [Project Structure](project-structure.md)

## Project Information

- **Owner**: ${{ values.owner }}
- **Component ID**: ${{ values.component_id }}
- **Repository**: [${{ values.destination }}](https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }})
- **.NET Version**: ${{ values.dotnetVersion }}
- **System**: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.projectName }}