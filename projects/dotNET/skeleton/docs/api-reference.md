# API Reference

This document describes the API endpoints provided by ${{ values.projectName }}.

## Base URL

When running locally, the base URL is: `http://localhost:5000`

## Endpoints

### GET /hello

Returns a greeting message from the application.

#### Request

```http
GET /hello
```

#### Response

```
200 OK
Content-Type: text/plain

Hello from ${{ values.projectName }}!
```

## OpenAPI Specification

Below is the OpenAPI specification for the ${{ values.component_id }} API:

```yaml
openapi: 3.0.0
info:
  title: ${{ values.component_id }}
  version: 1.0.0
  description: ${{ values.description }}
paths:
  /hello:
    get:
      summary: Get greeting
      responses:
        '200':
          description: A greeting message
          content:
            text/plain:
              schema:
                type: string
```

## Adding New Endpoints

When adding new endpoints to the application, please update this documentation to reflect the changes.
