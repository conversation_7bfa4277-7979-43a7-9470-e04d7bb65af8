# Project Structure

This document describes the structure of the ${{ values.projectName }} project.

## Root Directory

- `${{ values.projectName }}.csproj`: The .NET project file that defines the project configuration
- `Program.cs`: The entry point of the application and main configuration
- `README.md`: Project overview and basic instructions
- `.gitignore`: Specifies files that <PERSON><PERSON> should ignore
- `.gitlab-ci.yml`: GitLab CI/CD pipeline configuration
- `catalog-info.yaml`: Backstage catalog information
- `Dockerfile`: Docker container configuration for building and running the application

## Static Content

- `wwwroot/`: Directory for static files
  - `index.html`: The main HTML page served by the application

## Configuration

- `appsettings.json`: Application configuration
- `appsettings.Development.json`: Development-specific configuration

## Project File

The project file (`${{ values.projectName }}.csproj`) contains the following configuration:

```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>${{ values.dotnetVersion }}</TargetFramework>
    <AssemblyName>${{ values.projectName }}</AssemblyName>
    <RootNamespace>${{ values.projectName }}</RootNamespace>
    <Version>1.0.0</Version>
  </PropertyGroup>
</Project>
```

The `Version` property is used for versioning the application and Docker images.

## Main Application Code

The `Program.cs` file contains the application setup and routing:

```csharp
using Microsoft.AspNetCore.Builder;

var builder = WebApplication.CreateBuilder(args);

// Add services, if needed (e.g., controllers, Razor pages, etc.)
// builder.Services.AddRazorPages(); // Example

var app = builder.Build();

// Serve static files from wwwroot
app.UseDefaultFiles();
app.UseStaticFiles();

// Simple endpoint returning a custom hello message
app.MapGet("/hello", () => "Hello from ${{ values.projectName }}!");

app.Run();
```

## Backstage Integration

The `catalog-info.yaml` file defines how the project appears in Backstage:

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.component_id }}
  namespace: ${{ (values.repoUrl | parseRepoUrl).owner }}
  description: ${{ values.description }}
  annotations:
    gitlab.com/project-slug: ${{ values.destination }}
    backstage.io/techdocs-ref: dir:.
  links:
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}/-/pipelines
      title: GitLab Pipelines
      icon: pipeline
spec:
  type: project
  owner: ${{ values.owner }}
  lifecycle: experimental
  system: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.projectName }}
  providesApis:
    - ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.component_id }}
```

It also defines the System and API entities related to this component.

## Docker Configuration

The `Dockerfile` defines how the application is containerized:

```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:${{ values.dotnetVersion.substring(3) }} AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY *.csproj ./
RUN dotnet restore

# Copy everything else and build
COPY . ./
RUN dotnet publish -c Release -o /app/publish

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:${{ values.dotnetVersion.substring(3) }}
WORKDIR /app
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "${{ values.projectName }}.dll"]
```

This multi-stage Dockerfile:

1. Uses the .NET SDK image to build the application
2. Restores dependencies and publishes the application
3. Creates a smaller runtime image with just the published application
4. Sets the entry point to run the application
