# Development Guide

This guide provides information for developers working on the ${{ values.projectName }} project.

## Development Environment

### Recommended Tools

- **IDE**: Visual Studio 2022, Visual Studio Code with C# extension, or JetBrains Rider
- **API Testing**: Postman, Insomnia, or curl
- **Version Control**: Git

### Configuration

The application uses the standard ASP.NET Core configuration system. Configuration can be modified in:

- `appsettings.json`
- `appsettings.Development.json` (for development environment)
- Environment variables
- Command-line arguments

## Adding New Features

### Adding New API Endpoints

To add a new API endpoint, modify the `Program.cs` file:

```csharp
// Example of adding a new endpoint
app.MapGet("/api/status", () => new { Status = "OK", Timestamp = DateTime.UtcNow });
```

### Adding Static Content

Place any static files (HTML, CSS, JavaScript, images) in the `wwwroot` directory. They will be automatically served by the application.

### Adding Services

To add a new service:

1. Create a new service class
2. Register it in the dependency injection container in `Program.cs`:

```csharp
// Example of registering a service
builder.Services.AddSingleton<IMyService, MyService>();
```

## Testing

### Running Tests

```bash
dotnet test
```

### Adding Tests

Create test files in a separate test project and use xUnit, NUnit, or MSTest frameworks.

## Building for Production

```bash
dotnet publish -c Release
```

The published output will be in the `bin/Release/${{ values.dotnetVersion }}/publish` directory.

## Deployment

See the [CI/CD Pipeline](ci-cd.md) documentation for information on automated deployment.
