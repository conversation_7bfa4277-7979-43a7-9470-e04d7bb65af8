using Microsoft.AspNetCore.Builder;

var builder = WebApplication.CreateBuilder(args);

// Add services, if needed (e.g., controllers, Razor pages, etc.)
// builder.Services.AddRazorPages(); // Example

var app = builder.Build();

// Serve static files from wwwroot
app.UseDefaultFiles();
app.UseStaticFiles();

// Simple endpoint returning a custom hello message
app.MapGet("/hello", () => "Hello from ${{ values.projectName }}!");

app.Run();