stages:
  - build
  - publish
  - docker

variables:
  DOTNET_CLI_HOME: "/tmp/DOTNET_CLI_HOME"
  ENTITY_NAMESPACE: "${{ (values.repoUrl | parseRepoUrl).owner }}"
  ENTITY_KIND: "Component"
  ENTITY_NAME: ${{ values.component_id }}
  IMAGE_NAME: apps/${{ values.projectName | lower }}

default:
  image: mcr.microsoft.com/dotnet/sdk:${{ values.dotnetVersion.substring(3) }}

# -----------------------------------------------------------
# Stage 1: Build Docs
# -----------------------------------------------------------
build-docs:
  stage: build
  # Use an image that has Node and Python already available,
  # or pick a base image that you can apt-get install from.
  image: node:18-bullseye
  only:
    - main
  before_script:
    - apt-get update
    # Install Python 3 tooling
    - apt-get install -y python3 python3-pip curl graphviz default-jdk
  script:
    # 1. Install needed CLIs and dependencies
    - npm install -g @techdocs/cli
    - python3 -m pip install --upgrade pip
    - python3 -m pip install mkdocs-techdocs-core==1.*

    # 2. (Optional) PlantUML setup
    # - curl -o plantuml.jar -L http://sourceforge.net/projects/plantuml/files/plantuml.1.2021.4.jar/download
    # - echo "be498123d20eaea95a94b174d770ef94adfdca18  plantuml.jar" | sha1sum -c -
    # - mv plantuml.jar /opt/plantuml.jar
    # - mkdir -p "$HOME/.local/bin"
    # - echo '#!/bin/sh' > "$HOME/.local/bin/plantuml"
    # - echo 'java -jar /opt/plantuml.jar "$@"' >> "$HOME/.local/bin/plantuml"
    # - chmod +x "$HOME/.local/bin/plantuml"
    # - export PATH="$HOME/.local/bin:$PATH"

    # 3. Generate docs site
    - techdocs-cli generate --no-docker --verbose

  # Store the generated site/ folder so the next job can use it.
  artifacts:
    paths:
      - site
    expire_in: 1 day

# -----------------------------------------------------------
# Stage 2: Publish Docs
# -----------------------------------------------------------
publish-docs:
  stage: publish
  image: node:18-bullseye
  only:
    - main
  dependencies:
    - build-docs  # Ensure we use the artifacts from the build stage
  before_script:
    - apt-get update
    - apt-get install -y python3 python3-pip
    # Install TechDocs CLI here as well, if not reusing the same runner that already has it
    - npm install -g @techdocs/cli
  script:
    # Publish to Azure Blob Storage -- make sure to set these env variables in GitLab:
    # TECHDOCS_AZURE_BLOB_CONTAINER, TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME, TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY
    - techdocs-cli publish --publisher-type azureBlobStorage --storage-name "$TECHDOCS_AZURE_BLOB_CONTAINER" --azureAccountName "$TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME" --azureAccountKey "$TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY" --entity "$ENTITY_NAMESPACE/$ENTITY_KIND/$ENTITY_NAME"

build:
  stage: build
  script:
    - echo "Building ${{ values.projectName }} with ${{ values.dotnetVersion }}..."
    - dotnet restore
    - dotnet build --configuration Release --no-restore
    - dotnet publish --configuration Release --no-build --output ./publish
  artifacts:
    name: "${{ values.projectName }}-app"
    paths:
      - ./publish/
    expire_in: 1 week

# -----------------------------------------------------------
# Stage 3: Build and Push Docker Image
# -----------------------------------------------------------
docker-build-push:
  stage: docker
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    # Extract version from csproj file
    - apk add --no-cache grep libxml2-utils git
    - VERSION=$(grep -oP '<Version>\K[^<]+' *.csproj)
    - COMMIT_SHA=$(git rev-parse --short=6 HEAD)
    - VERSION_TAG="$VERSION-$COMMIT_SHA"
    - echo "Building image with version $VERSION_TAG"
    # Login to ACR
    - echo "$ACR_PASSWORD" | docker login $ACR_REGISTRY -u $ACR_USERNAME --password-stdin
  script:
    # Build and push Docker image
    - docker build -t $ACR_REGISTRY/$IMAGE_NAME:latest -t $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG .
    - docker push $ACR_REGISTRY/$IMAGE_NAME:latest
    - docker push $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG
  only:
    - main