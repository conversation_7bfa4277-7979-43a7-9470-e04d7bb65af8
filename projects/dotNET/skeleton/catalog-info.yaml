apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.component_id }}
  namespace: ${{ (values.repoUrl | parseRepoUrl).owner }}
  description: ${{ values.description | dump }}
  annotations:
    gitlab.com/project-slug: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.projectName }}
    backstage.io/kubernetes-id: ${{ values.component_id }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
    argocd/app-name: ${{ values.component_id }}-${{ values.env }}
    backstage.io/techdocs-ref: dir:.
  links:
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}/-/pipelines
      title: GitLab Pipelines
      icon: pipeline
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}
      title: Repository
      icon: github
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}/-/blob/main/docs
      title: Documentation
      icon: doc
spec:
  type: project
  owner: ${{ values.owner }}
  lifecycle: experimental
  system: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.projectName }}
  providesApis:
    - ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.component_id }}

---
apiVersion: backstage.io/v1alpha1
kind: System
metadata:
  name: ${{ values.projectName }}
  namespace: ${{ (values.repoUrl | parseRepoUrl).owner }}
  description: System for ${{ values.projectName }}
spec:
  owner: ${{ values.owner }}
  domain: default

---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: ${{ values.component_id }}
  namespace: ${{ (values.repoUrl | parseRepoUrl).owner }}
  description: API provided by ${{ values.component_id | dump }}
spec:
  type: openapi
  lifecycle: experimental
  owner: ${{ values.owner }}
  system: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ values.projectName }}
  definition: |
    openapi: 3.0.0
    info:
      title: ${{ values.component_id }}
      version: 1.0.0
      description: ${{ values.description }}
    paths:
      /hello:
        get:
          summary: Get greeting
          responses:
            '200':
              description: A greeting message
              content:
                text/plain:
                  schema:
                    type: string
