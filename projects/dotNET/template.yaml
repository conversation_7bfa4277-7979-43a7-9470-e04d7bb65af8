apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: netapp-template
  title: .NET Web Application
  description: Create a basic ASP.NET Core web application
  tags:
    - recommended
    - dotnet
    - web
    - csharp
spec:
  owner: ESET
  type: service
  parameters:
    - title: Provide information about the new application
      required:
        - owner
        - repoUrl
        - projectName
        - dotnetVersion
        - description
      properties:
        projectName:
          title: Project Name
          type: string
          ui:placeholder: "MyNetProject"
          ui:help: "Project name for the .NET application (will be used as the component name). Must begin with an alphanumeric character and contain only alphanumeric characters, hyphens, or underscores."
          default: MyNetProject
          pattern: '^[a-zA-Z0-9][a-zA-Z0-9-_]*$'
          minLength: 2
          maxLength: 64
        description:
          title: Description
          type: string
          ui:placeholder: "e.g. A web API for managing users"
          ui:help: "Briefly describe the purpose of this component/application."
        dotnetVersion:
          title: .NET Version
          type: string
          ui:placeholder: "net8.0"
          ui:help: "Select the target .NET version for the project."
          enum:
            - net6.0
            - net7.0
            - net8.0
            - net9.0
          default: net9.0
        owner:
          title: Owner
          type: string
          ui:placeholder: "team-backend"
          ui:help: "Select the group or user who will own this component."
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
        repoUrl:
          title: Repository Location
          type: string
          ui:placeholder: "Choose or enter a repository URL"
          ui:help: "Where to create the Git repository for this application (GitLab only)."
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - "gitlab.cluster.eset.corp"
              - "gitlab.cluster.eset.systems"
            allowedOwners:
              - sigproject

    - title: "Deployment & ArgoCD Configuration"
      required:
        - env
        - gitlabHost
        - gitlabOwner
        - argocdRepoName
        # - acrRegistry # Removed this duplicate parameter
        - imageTag # Added for deployment image tag
        - acrRegistryUrl # Added for ACR URL
      properties:
        env:
          title: Environment
          type: string
          ui:placeholder: "deva"
          ui:help: "Deployment environment: 'deva' for development or 'prod' for production."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        gitlabHost:
          title: "GitLab Host"
          type: string
          ui:placeholder: "gitlab.cluster.eset.systems"
          ui:help: "Hostname of the GitLab instance where the repos are hosted."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        gitlabOwner:
          title: "GitLab Owner"
          type: string
          ui:placeholder: "sigproject"
          ui:help: "GitLab user or group name that owns the associated repositories."
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage application component definitions."
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository name for ArgoCD application manifests."
          default: "backstage-argocd-apps"
        acrRegistryUrl:
          title: "ACR Registry URL"
          type: string
          ui:placeholder: "escdev.azurecr.io"
          ui:help: "Azure Container Registry (ACR) URL that will be used for images, e.g. escdev.azurecr.io."
          default: "escdev.azurecr.io"
        imageTag:
          title: "Image Tag"
          type: string
          ui:placeholder: "latest"
          ui:help: "The Docker image tag to deploy (e.g., latest, v1.0.0)."
          default: "latest"
        replicas:
          title: "Replicas"
          type: number
          ui:placeholder: 1
          ui:help: "Number of replicas for the deployment in the selected environment."
          default: 1
        containerPort:
          title: "Container Port"
          type: number
          ui:placeholder: 8080
          ui:help: "Port inside the container on which the application listens."
          default: 8080
        servicePort:
          title: "Service Port"
          type: number
          ui:placeholder: 80
          ui:help: "Port to expose via the Kubernetes Service."
          default: 80

  steps:
    - id: template
      name: Fetch Template
      action: fetch:template
      input:
        url: ./skeleton
        values:
          component_id: ${{ parameters.projectName | lower }}
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.projectName | lower }}
          description: ${{ parameters.description }}
          owner: ${{ parameters.owner }}
          destination: ${{ parameters.projectName | lower }}
          dotnetVersion: ${{ parameters.dotnetVersion }}
          repoUrl: ${{ parameters.repoUrl }}
          env: ${{ parameters.env }}
    - id: rename-files
      name: Rename Project Files
      action: fs:rename
      input:
        files:
          - from: HelloWorldProject.csproj
            to: ${{ parameters.projectName }}.csproj
    - id: publish
      name: Publish to GitLab
      action: publish:gitlab
      input:
        repoUrl: ${{ parameters.repoUrl }}
        defaultBranch: main
        projectVariables:
        - key: TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME
          value: backstagetechdocsesc
          description: Azure Blob Storage Account Name
        - key: TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY
          value: "****************************************************************************************"
          description: Azure Blob Storage Account Key
          protected: true
        - key: TECHDOCS_AZURE_BLOB_CONTAINER
          value: docs
          description: Azure Blob Storage Container Name
        - key: ACR_REGISTRY
          value: ${{ parameters.acrRegistryUrl }} # Use the parameter here
          description: Azure Container Registry URL
        - key: ACR_USERNAME
          value: backstage-apps-write
          description: Azure Container Registry Username
        - key: ACR_PASSWORD
          value: "z9mpe3WiGSQWL4VhYLtBuMgq2qpYRGWsoBHVV4Ht3d+ACRAlsbLO"
          description: Azure Container Registry Password
          protected: true
        # IMAGE_NAME is now defined directly in the .gitlab-ci.yml file

    # --- Deployment Manifest Steps Start ---

    # 1. Fetch Kustomize Base files
    - id: fetch-deployment-base
      name: Fetch Kustomize Base Manifests
      action: fetch:template
      input:
        url: ./deployment/base # Location of base templates
        targetPath: ./deployment-output/base # Temporary output dir for base
        values:
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.projectName | lower }}
          acrRegistry: ${{ parameters.acrRegistryUrl }} # Use the parameter here
          containerPort: ${{ parameters.containerPort }}
          servicePort: ${{ parameters.servicePort }}

    # 2. Fetch Kustomize Overlay files for the target environment (Aligned comment)
    - id: fetch-deployment-overlay
      name: Fetch Kustomize Overlay Manifests
      action: fetch:template
      input:
        url: ./deployment/overlays # Location of overlay templates (*.tpl)
        # Create overlay dir specific to environment
        targetPath: ./deployment-output/overlays/${{ parameters.env }}
        values:
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.projectName | lower }}
          acrRegistry: ${{ parameters.acrRegistryUrl }} # Use the parameter here
          imageTag: ${{ parameters.imageTag }}
          replicas: ${{ parameters.replicas }}
          containerPort: ${{ parameters.containerPort }}
          servicePort: ${{ parameters.servicePort }}
          # Add any other overlay-specific values here

    # 3. Rename overlay template files from .tpl to .yaml
    - id: rename-overlay-files
      name: Rename Overlay Manifest Files
      action: fs:rename
      input:
        files:
          - from: ./deployment-output/overlays/${{ parameters.env }}/kustomization.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/kustomization.yaml
          - from: ./deployment-output/overlays/${{ parameters.env }}/deployment-patch.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/deployment-patch.yaml
          - from: ./deployment-output/overlays/${{ parameters.env }}/service-patch.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/service-patch.yaml

    # 4. Push Deployment Manifests to Application Git Repository
    - id: publish-deployment-manifests
      name: Publish Deployment Manifests to App Repo
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabHost }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.gitlabOwner }}
        branchName: main
        sourcePath: ./deployment-output # Push the whole structure (base + overlays/env)
        targetPath: /${{ parameters.env }}/dotnet/${{ parameters.projectName | lower }}
        commitMessage: Add initial Kustomize deployment manifests for app ${{ parameters.projectName }} in the ${{ parameters.env }} environment

    # --- Deployment Manifest Steps End ---

    # --- ArgoCD Steps Start ---

    # 5. Generate ArgoCD application configuration
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./argocd-config # Directory containing the argocd-application.yaml template
        values:
          name: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.projectName | lower }}
          # Construct SSH URL using provided host, owner, and derived app repo name from repoUrl
          dotnetResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.gitlabOwner }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        # Use a temporary output path
        targetPath: ./argocd-config-output

    # 6. Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config-output/argocd-application.yaml
            # Rename to include component name and type
            to: ./argocd-config-output/${{ parameters.projectName | lower }}-dotnet-application.yaml

    # 7. Create MR for ArgoCD config to GitLab ArgoCD Repo
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        # Use the dedicated ArgoCD repo parameters
        repoUrl: ${{ parameters.gitlabHost }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.gitlabOwner }}
        branchName: feature/${{ parameters.projectName | lower }}-argocd-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config-output # Path containing the renamed file
        # Target path specific to dotnet apps in the specified environment
        targetPath: ${{ parameters.env }}/dotnet
        description: "Add ArgoCD configuration for .NET application '${{ parameters.projectName | lower }}' in namespace '${{ parameters.projectName | lower }}' for environment '${{ parameters.env }}'"
        title: "feat: Add '${{ parameters.projectName | lower }}' ArgoCD config (${{ parameters.env }})"

    # --- ArgoCD Steps End ---

    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      # New link - Ensure it starts with '-' at the correct level under 'links'
      - title: View Deployment Manifests in App Repo
        # Link to the deployment folder created in the app repo
        url: https://${{ parameters.gitlabHost }}/${{ parameters.gitlabOwner }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/dotnet/${{ parameters.projectName | lower }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
