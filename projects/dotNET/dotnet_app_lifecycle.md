# .NET App Creation Lifecycle (Developer View)

This diagram shows what happens when you create a new .NET app using the Backstage template.  
**Phases are color-coded and visually separated for clarity.**

```mermaid
flowchart TD
    %% Nodes
    user([🧑‍💻 Start template in Backstage])
    repo([📦 GitLab repo created & code pushed])
    deploy_files([📝 Deployment & ArgoCD config generated])
    mr([🔀 Merge Request opened in ArgoCD repo])
    review{🔍 MR reviewed & merged}
    deploy([🚀 ArgoCD deploys app to Kubernetes])
    catalog([📚 App appears in Backstage Catalog])

    ci1([🏗️ CI/CD: Build app, Docker image, docs])
    ci2([📤 CI/CD: Push Docker image to registry])

    %% Main flow
    user --> repo --> deploy_files --> mr --> review --> deploy --> catalog

    %% Async pipeline
    repo -.-> ci1 --> ci2

    %% Classes for color
    classDef template fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1;
    classDef pipeline fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#e65100;
    classDef mr fill:#f3e5f5,stroke:#8e24aa,stroke-width:2px,color:#4a148c;
    classDef deploy fill:#e8f5e9,stroke:#388e3c,stroke-width:2px,color:#1b5e20;

    class user,repo,deploy_files template;
    class ci1,ci2 pipeline;
    class mr,review mr;
    class deploy,catalog deploy;
```

**Legend:**
- <span style="color:#1976d2;">🟦 Blue</span>: Template steps (Backstage)
- <span style="color:#f57c00;">🟧 Orange</span>: GitLab CI/CD pipeline (runs async)
- <span style="color:#8e24aa;">🟪 Purple</span>: ArgoCD Merge Request & review
- <span style="color:#388e3c;">🟩 Green</span>: Deployment & catalog

**Notes:**
- The pipeline (orange) runs automatically after the repo is created and does not block the MR review or deployment.
- The ArgoCD Merge Request must be reviewed and merged before deployment.
- After deployment, your app is visible in Backstage and ready to use.