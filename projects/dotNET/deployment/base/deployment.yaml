apiVersion: apps/v1
kind: Deployment
metadata:
  # Name will be set using commonLabels in kustomization.yaml
  name: ${{ values.projectName | lower }}-deployment # Base name
spec:
  # Replicas will be patched by overlay
  replicas: 1 # Default base replica count
  selector:
    matchLabels:
      # Selector labels should match template labels
      app.kubernetes.io/name: ${{ values.projectName | lower }}
  template:
    metadata:
      labels:
        # Pod labels will inherit commonLabels from kustomization.yaml
        app.kubernetes.io/name: ${{ values.projectName | lower }}
    spec:
      imagePullSecrets:
        - name: backstage-apps-read
      containers:
        - name: app
          # Image will be patched by overlay
          image: ${{ values.acrRegistry }}/apps/${{ values.projectName | lower }}:latest # Updated image path with 'apps' prefix
          ports:
            - name: http
              containerPort: ${{ values.containerPort }} # Port the application listens on inside the container
              protocol: TCP
          # Add readiness/liveness probes as needed
          # readinessProbe:
          #   httpGet:
          #     path: /healthz # Adjust path as needed
          #     port: http
          # livenessProbe:
          #   httpGet:
          #     path: /healthz # Adjust path as needed
          #     port: http
          # Add resource requests/limits as needed (can be patched)
          # resources:
          #   requests:
          #     memory: "64Mi"
          #     cpu: "250m"
          #   limits:
          #     memory: "128Mi"
          #     cpu: "500m"