apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Define the namespace for all resources in this base
namespace: ${{ values.namespace | lower }}

resources:
  - namespace.yaml
  - deployment.yaml
  - service.yaml
  - secret.yaml

# Common labels to add to all resources
commonLabels:
  app.kubernetes.io/name: ${{ values.projectName | lower }}
  app.kubernetes.io/part-of: ${{ values.projectName | lower }}
  backstage.io/kubernetes-id: ${{ values.projectName | lower }}

# Common annotations (optional)
# commonAnnotations:
#   backstage.io/kubernetes-id: ${{ values.projectName | lower }}

# Image overrides (will be patched in overlays usually)
# images:
#   - name: ${{ values.acrRegistry }}/${{ values.projectName | lower }} # Placeholder, specific tag in overlay
#     newTag: latest # Placeholder