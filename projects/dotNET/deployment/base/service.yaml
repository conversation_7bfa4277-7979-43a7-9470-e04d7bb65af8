apiVersion: v1
kind: Service
metadata:
  # Name will be set using commonLabels in kustomization.yaml
  name: ${{ values.projectName | lower }}-service # Base name
spec:
  selector:
    # Selector must match the labels on the Pods
    app.kubernetes.io/name: ${{ values.projectName | lower }}
  ports:
    - name: http
      protocol: TCP
      port: ${{ values.servicePort }} # Port the service exposes
      targetPort: http # Name of the port on the container (from deployment.yaml)
  # Type can be patched by overlay if needed (e.g., LoadBalancer for prod)
  type: ClusterIP # Default service type