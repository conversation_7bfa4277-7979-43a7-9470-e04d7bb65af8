# Template for overlay service-patch.yaml
# Patches the base service with environment-specific settings.
apiVersion: v1
kind: Service
metadata:
  # Must match the name of the service in the base
  name: ${{ values.projectName | lower }}-service
spec:
  # Uncomment to override service port if needed
  # ports:
  #   - name: http
  #     protocol: TCP
  #     port: ${{ values.servicePort }} # Override service port if needed
  #     targetPort: http
  # Uncomment to override service type if needed
  # type: ClusterIP # Default service type
