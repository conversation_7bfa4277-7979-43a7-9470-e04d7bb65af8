# Template for overlay kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Point to the base directory relative to this overlay
resources:
  - ../../base

# Specify the namespace for this specific environment overlay
# This ensures resources are created/managed in the correct namespace
# even if the base kustomization also defines it.
namespace: ${{ values.namespace | lower }}

# Patches specific to this environment
patchesStrategicMerge:
  - deployment-patch.yaml
  # Uncomment to use service patch if needed
  # - service-patch.yaml

# Update image tags for this environment
images:
  - name: ${{ values.acrRegistry }}/${{ values.projectName | lower }} # Match the image name used in base/deployment.yaml
    newTag: ${{ values.imageTag }} # Use the specific tag for this environment