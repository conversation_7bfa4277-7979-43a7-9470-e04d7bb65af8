# Template for overlay deployment-patch.yaml
# Patches the base deployment with environment-specific settings.
apiVersion: apps/v1
kind: Deployment
metadata:
  # Must match the name of the deployment in the base
  name: ${{ values.projectName | lower }}-deployment
spec:
  # Override replica count for this environment
  replicas: ${{ values.replicas | default(1) }} # Allow overriding replicas, default to 1
  template:
    spec:
      containers:
        - name: app # Must match the container name in the base deployment
          # Uncomment to override container port if needed
          # ports:
          #   - name: http
          #     containerPort: ${{ values.containerPort }} # Override container port if needed
          #     protocol: TCP
          # Add environment-specific variables if needed
          # env:
          #   - name: ASPNETCORE_ENVIRONMENT
          #     value: ${{ values.aspNetCoreEnvironment | default('Development') }} # Example
          # Override resource limits/requests if needed for this environment
          # resources:
          #   requests:
          #     memory: "128Mi"
          #     cpu: "500m"
          #   limits:
          #     memory: "256Mi"
          #     cpu: "1"