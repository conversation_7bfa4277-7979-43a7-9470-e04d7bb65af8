apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.name }}-${{ values.env }}
  namespace: ec-argocd # Assuming this namespace is standard
spec:
  project: default # Assuming this project is standard
  source:
    # The repoURL will need to be constructed in the main template.yaml
    repoURL: ${{ values.dotnetResourcesRepoUrl }}
    targetRevision: HEAD
    # Updated path for dotnet applications
    path: ${{ values.env }}/dotnet/${{ values.name }}/overlays/${{ values.env }}
  destination:
    # This might need parameterization if different clusters are targeted
    name: idp-test
    # Namespace where the .NET app itself will be deployed
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated: {}
    syncOptions:
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true