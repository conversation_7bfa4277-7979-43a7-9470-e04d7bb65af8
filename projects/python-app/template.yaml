apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: python-app-template
  title: Python Web Application
  description: Create a Python web application with FastAPI, Kafka, and PostgreSQL
  tags:
    - recommended
    - python
    - web
    - fastapi
    - kafka
    - postgresql
spec:
  owner: ESET
  type: service
  parameters:
    - title: Provide information about the new application
      required:
        - owner
        - repoUrl
        - projectName
        - pythonVersion
        - description
      properties:
        projectName:
          title: Project Name
          type: string
          ui:placeholder: "MyPythonApp"
          ui:help: "Project name for the Python application (will be used as the component name). Must begin with an alphanumeric character and contain only alphanumeric characters, hyphens, or underscores."
          default: MyPythonApp
          pattern: '^[a-zA-Z0-9][a-zA-Z0-9-_]*$'
          minLength: 2
          maxLength: 64
        description:
          title: Description
          type: string
          ui:placeholder: "e.g. A web API for managing tasks"
          ui:help: "Briefly describe the purpose of this component/application."
        pythonVersion:
          title: Python Version
          type: string
          ui:placeholder: "3.12"
          ui:help: "Select the target Python version for the project."
          enum:
            - "3.10"
            - "3.11"
            - "3.12"
            - "3.13"
          default: "3.12"
        owner:
          title: Owner
          type: string
          ui:placeholder: "team-backend"
          ui:help: "Select the group or user who will own this component."
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
        repoUrl:
          title: Repository Location
          type: string
          ui:placeholder: "Choose or enter a repository URL"
          ui:help: "Where to create the Git repository for this application (GitLab only)."
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - "gitlab.cluster.eset.corp"
              - "gitlab.cluster.eset.systems"
            allowedOwners:
              - sigproject

    - title: "Kafka Configuration"
      required:
        - kafkaCluster
        - kafkaTopic
        - kafkaUsername
      properties:
        kafkaCluster:
          title: Kafka Cluster
          type: string
          ui:field: EntityPicker
          ui:options:
            catalogFilter:
              - kind: Component
                spec.type: kafka-cluster
            defaultKind: Component
            allowArbitraryValues: false
          ui:help: "Select an existing Kafka cluster from the catalog."
        kafkaTopic:
          title: Kafka Topic
          type: string
          ui:placeholder: "python-app-messages"
          ui:help: "Kafka topic name for this application."
          default: "python-app-messages"
        kafkaUsername:
          title: Kafka Username
          type: string
          ui:placeholder: "user"
          ui:help: "Kafka username for authentication (will be used as secret name for certificates)."
          default: "user"

    - title: "Database Configuration"
      required:
        - dbCluster
        - dbUsername
        - dbPassword
        - dbName
      properties:
        dbCluster:
          title: PostgreSQL Cluster
          type: string
          ui:field: EntityPicker
          ui:options:
            catalogFilter:
              - kind: Component
                spec.type: postgres-cluster
            defaultKind: Component
            allowArbitraryValues: false
          ui:help: "Select an existing PostgreSQL cluster from the catalog."
        dbUsername:
          title: Database Username
          type: string
          ui:placeholder: "todoapp_user"
          ui:help: "Username for database connection."
          default: "todoapp_user"
        dbPassword:
          title: Database Password
          type: string
          ui:placeholder: "admin123"
          ui:help: "Password for database connection."
          ui:field: Secret
          default: "admin123"
        dbName:
          title: Database Name
          type: string
          ui:placeholder: "todoapp"
          ui:help: "Name of the database to connect to."
          default: "todoapp"

    - title: "Application Access Configuration"
      required:
        - appUrl
        - ingressClassName
      properties:
        appUrl:
          title: Application URL Prefix
          type: string
          ui:placeholder: "python-app"
          ui:help: "URL prefix for your application. Full URL will be: {prefix}.idp-test.deva.esc.esetrs.cz"
          default: "python-app-v2"
          pattern: '^[a-zA-Z0-9][a-zA-Z0-9-]*$'
        ingressClassName:
          title: Ingress Class Name
          type: string
          ui:placeholder: "python-app"
          ui:help: "Name of the ingress class to use for routing traffic (e.g., nginx, nginx-public, python-app, etc.)"
          default: "python-app"

    - title: "Deployment & ArgoCD Configuration"
      required:
        - env
        - namespace
        - gitlabHost
        - gitlabOwner
        - argocdRepoName
        - imageTag
        - acrRegistryUrl
      properties:
        namespace:
          title: Target Namespace
          type: string
          ui:placeholder: "python-app"
          ui:help: "Kubernetes namespace where the application will be deployed."
          default: "python-app"
          pattern: '^[a-z0-9]([-a-z0-9]*[a-z0-9])?$'
          minLength: 1
          maxLength: 63
        env:
          title: Environment
          type: string
          ui:placeholder: "deva"
          ui:help: "Deployment environment: 'deva' for development or 'prod' for production."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        gitlabHost:
          title: "GitLab Host"
          type: string
          ui:placeholder: "gitlab.cluster.eset.corp"
          ui:help: "Hostname of the GitLab instance where the repos are hosted."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        gitlabOwner:
          title: "GitLab Owner"
          type: string
          ui:placeholder: "sigproject"
          ui:help: "GitLab user or group name that owns the associated repositories."
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage application component definitions."
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository name for ArgoCD application manifests."
          default: "backstage-argocd-apps"
        acrRegistryUrl:
          title: "ACR Registry URL"
          type: string
          ui:placeholder: "escdev.azurecr.io"
          ui:help: "Azure Container Registry (ACR) URL that will be used for images, e.g. escdev.azurecr.io."
          default: "escdev.azurecr.io"
        imageTag:
          title: "Image Tag"
          type: string
          ui:placeholder: "latest"
          ui:help: "The Docker image tag to deploy (e.g., latest, v1.0.0)."
          default: "latest"
        replicas:
          title: "Replicas"
          type: number
          ui:placeholder: 1
          ui:help: "Number of replicas for the deployment in the selected environment."
          default: 1
        containerPort:
          title: "Container Port"
          type: number
          ui:placeholder: 8000
          ui:help: "Port inside the container on which the application listens."
          default: 8000
        servicePort:
          title: "Service Port"
          type: number
          ui:placeholder: 80
          ui:help: "Port to expose via the Kubernetes Service."
          default: 80

  steps:
    - id: template
      name: Fetch Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutTemplating:
          - 'app/templates/**'
        values:
          component_id: ${{ parameters.projectName | lower }}
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.namespace }}
          description: ${{ parameters.description }}
          owner: ${{ parameters.owner }}
          destination: ${{ parameters.projectName | lower }}
          pythonVersion: ${{ parameters.pythonVersion }}
          repoUrl: ${{ parameters.repoUrl }}
          env: ${{ parameters.env }}
          gitlabHost: ${{ parameters.gitlabHost }}
          gitlabOwner: ${{ parameters.gitlabOwner }}
          kafkaClusterName: ${{ parameters.kafkaCluster | parseEntityRef | pick('name') }}
          kafkaTopic: ${{ parameters.kafkaTopic }}
          kafkaUsername: ${{ parameters.kafkaUsername }}
          dbClusterName: ${{ parameters.dbCluster | parseEntityRef | pick('name') }}
          dbUsername: ${{ parameters.dbUsername }}
          dbPassword: ${{ secrets.dbPassword }}
          dbName: ${{ parameters.dbName }}
          appUrl: ${{ parameters.appUrl }}
          dbConnectionUrl: postgresql+asyncpg://${{ parameters.dbUsername }}:${{ secrets.dbPassword }}@${{ parameters.dbCluster | parseEntityRef | pick('name') }}-${{ parameters.projectName | lower }}.${{ parameters.namespace }}.svc.deva-idp-test:5432/${{ parameters.dbName }}

    - id: publish
      name: Publish to GitLab
      action: publish:gitlab
      input:
        repoUrl: ${{ parameters.repoUrl }}
        defaultBranch: main
        projectVariables:
        - key: TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME
          value: backstagetechdocsesc
          description: Azure Blob Storage Account Name
        - key: TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY
          value: "****************************************************************************************"
          description: Azure Blob Storage Account Key
          protected: true
        - key: TECHDOCS_AZURE_BLOB_CONTAINER
          value: docs
          description: Azure Blob Storage Container Name
        - key: ACR_REGISTRY
          value: ${{ parameters.acrRegistryUrl }}
          description: Azure Container Registry URL
        - key: ACR_USERNAME
          value: backstage-apps-write
          description: Azure Container Registry Username
        - key: ACR_PASSWORD
          value: "z9mpe3WiGSQWL4VhYLtBuMgq2qpYRGWsoBHVV4Ht3d+ACRAlsbLO"
          description: Azure Container Registry Password
          protected: true

    # --- Deployment Manifest Steps Start ---

    # 1. Fetch Kustomize Base files
    - id: fetch-deployment-base
      name: Fetch Kustomize Base Manifests
      action: fetch:template
      input:
        url: ./deployment/base
        targetPath: ./deployment-output/base
        values:
          component_id: ${{ parameters.projectName | lower }}
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.namespace }}
          acrRegistry: ${{ parameters.acrRegistryUrl }}
          containerPort: ${{ parameters.containerPort }}
          servicePort: ${{ parameters.servicePort }}

    # 2. Fetch Kustomize Overlay files for the target environment
    - id: fetch-deployment-overlay
      name: Fetch Kustomize Overlay Manifests
      action: fetch:template
      input:
        url: ./deployment/overlays
        targetPath: ./deployment-output/overlays/${{ parameters.env }}
        values:
          component_id: ${{ parameters.projectName | lower }}
          projectName: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.namespace }}
          acrRegistry: ${{ parameters.acrRegistryUrl }}
          imageTag: ${{ parameters.imageTag }}
          replicas: ${{ parameters.replicas }}
          containerPort: ${{ parameters.containerPort }}
          servicePort: ${{ parameters.servicePort }}
          kafkaClusterName: ${{ parameters.kafkaCluster | parseEntityRef | pick('name') }}
          kafkaTopic: ${{ parameters.kafkaTopic }}
          kafkaUsername: ${{ parameters.kafkaUsername }}
          dbConnectionUrl: postgresql+asyncpg://${{ parameters.dbUsername }}:${{ secrets.dbPassword }}@${{ parameters.dbCluster | parseEntityRef | pick('name') }}.${{ parameters.namespace }}.svc.deva-idp-test:5432/${{ parameters.dbName }}
          appUrl: ${{ parameters.appUrl }}
          ingressClassName: ${{ parameters.ingressClassName }}
          envSuffix: ${{ parameters.env }}

    # 3. Rename overlay template files from .tpl to .yaml
    - id: rename-overlay-files
      name: Rename Overlay Manifest Files
      action: fs:rename
      input:
        files:
          - from: ./deployment-output/overlays/${{ parameters.env }}/kustomization.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/kustomization.yaml
          - from: ./deployment-output/overlays/${{ parameters.env }}/deployment-patch.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/deployment-patch.yaml
          - from: ./deployment-output/overlays/${{ parameters.env }}/ingress-patch.yaml.tpl
            to: ./deployment-output/overlays/${{ parameters.env }}/ingress-patch.yaml

    # 4. Push Deployment Manifests to Application Git Repository
    - id: publish-deployment-manifests
      name: Publish Deployment Manifests to App Repo
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabHost }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.gitlabOwner }}
        branchName: main
        sourcePath: ./deployment-output
        targetPath: /${{ parameters.env }}/python/${{ parameters.projectName | lower }}
        commitMessage: Add initial Kustomize deployment manifests for app ${{ parameters.projectName }} in the ${{ parameters.env }} environment

    # --- Deployment Manifest Steps End ---

    # --- ArgoCD Steps Start ---

    # 5. Generate ArgoCD application configuration
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          name: ${{ parameters.projectName | lower }}
          namespace: ${{ parameters.namespace }}
          pythonResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.gitlabOwner }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        targetPath: ./argocd-config-output

    # 6. Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config-output/argocd-application.yaml
            to: ./argocd-config-output/${{ parameters.projectName | lower }}-python-application.yaml

    # 7. Create MR for ArgoCD config to GitLab ArgoCD Repo
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabHost }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.gitlabOwner }}
        branchName: feature/${{ parameters.projectName | lower }}-argocd-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config-output
        targetPath: ${{ parameters.env }}/python
        description: "Add ArgoCD configuration for Python application '${{ parameters.projectName | lower }}' in namespace '${{ parameters.namespace }}' for environment '${{ parameters.env }}'"
        title: "feat: Add '${{ parameters.projectName | lower }}' ArgoCD config (${{ parameters.env }})"

    # --- ArgoCD Steps End ---

    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: View Deployment Manifests in App Repo
        url: https://${{ parameters.gitlabHost }}/${{ parameters.gitlabOwner }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/python/${{ parameters.projectName | lower }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}