apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${{ values.projectName }}
  namespace: ${{ values.namespace }}
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: ${{ values.ingressClassName }}
  rules:
  - host: ${{ values.appUrl }}.idp-test.${{ values.envSuffix }}.esc.esetrs.cz
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ${{ values.projectName }}
            port:
              number: ${{ values.servicePort }}
  tls:
  - hosts:
    - ${{ values.appUrl }}.idp-test.${{ values.envSuffix }}.esc.esetrs.cz
    secretName: ${{ values.projectName }}-tls-secret