apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${{ values.projectName }}
spec:
  replicas: ${{ values.replicas }}
  template:
    spec:
      containers:
      - name: ${{ values.projectName }}
        image: ${{ values.acrRegistry }}/apps/${{ values.projectName }}:${{ values.imageTag }}
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
          seccompProfile:
            type: RuntimeDefault
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ${{ values.projectName }}-db-secret
              key: database-url
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "${{ values.kafkaClusterName }}-kafka-external-bootstrap:9294"
        - name: KAFKA_TOPIC
          value: "${{ values.kafkaTopic }}"
        - name: KAFKA_SECURITY_PROTOCOL
          value: "SSL"
        - name: KAF<PERSON>_SSL_CAFILE
          value: "/kafka-certs/ca.crt"
        - name: KAFKA_SSL_CERTFILE
          value: "/kafka-certs/user.crt"
        - name: KAFKA_SSL_KEYFILE
          value: "/kafka-certs/user.key"
        volumeMounts:
        - name: kafka-certs
          mountPath: /kafka-certs
          readOnly: true
      volumes:
      - name: kafka-certs
        secret:
          secretName: ${{ values.kafkaUsername }}
          items:
          - key: ca.crt
            path: ca.crt
          - key: user.crt
            path: user.crt
          - key: user.key
            path: user.key