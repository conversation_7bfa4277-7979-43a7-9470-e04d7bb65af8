apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ${{ values.namespace }}

resources:
  - ../../base

patches:
  - path: deployment-patch.yaml
  - path: ingress-patch.yaml

secretGenerator:
  - name: ${{ values.projectName }}-db-secret
    type: Opaque
    literals:
      - database-url=${{ values.dbConnectionUrl }}
  - name: backstage-apps-read
    type: kubernetes.io/dockerconfigjson
    literals:
      - .dockerconfigjson={"auths":{"escdev.azurecr.io":{"username":"backstage-apps-read","password":"****************************************************","email":"<EMAIL>","auth":"************************************************************************************************"}}}

configMapGenerator:
  - name: ${{ values.projectName }}-kafka-config
    literals:
      - KAFKA_GROUP_ID=${{ values.projectName }}-group
      - KAFKA_CLIENT_ID=${{ values.projectName }}-client

replicas:
  - name: ${{ values.projectName }}
    count: ${{ values.replicas }}

labels:
  - pairs:
      environment: kafka
      app.kubernetes.io/instance: ${{ values.projectName }}-kafka
    includeSelectors: true

namePrefix: kafka-