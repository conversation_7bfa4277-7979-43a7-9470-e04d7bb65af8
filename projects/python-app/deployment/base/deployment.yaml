apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${{ values.projectName }}
  labels:
    app: ${{ values.projectName }}
    backstage.io/kubernetes-id: ${{ values.component_id }}
    app.kubernetes.io/name: ${{ values.component_id }}
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ${{ values.component_id }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${{ values.projectName }}
  template:
    metadata:
      labels:
        app: ${{ values.projectName }}
        backstage.io/kubernetes-id: ${{ values.component_id }}
        app.kubernetes.io/name: ${{ values.component_id }}
        app.kubernetes.io/component: backend
        app.kubernetes.io/part-of: ${{ values.component_id }}
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      imagePullSecrets:
      - name: backstage-apps-read
      containers:
      - name: ${{ values.projectName }}
        image: ${{ values.acrRegistry }}/apps/${{ values.projectName }}:latest
        imagePullPolicy: Always
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
        ports:
        - containerPort: ${{ values.containerPort }}
          name: http
        env:
        - name: APP_HOST
          value: "0.0.0.0"
        - name: APP_PORT
          value: "${{ values.containerPort }}"
        - name: DEBUG
          value: "false"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5