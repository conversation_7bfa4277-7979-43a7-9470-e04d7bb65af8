apiVersion: v1
kind: Service
metadata:
  name: ${{ values.projectName }}
  labels:
    app: ${{ values.projectName }}
    backstage.io/kubernetes-id: ${{ values.component_id }}
    app.kubernetes.io/name: ${{ values.component_id }}
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ${{ values.component_id }}
spec:
  type: ClusterIP
  ports:
  - port: ${{ values.servicePort }}
    targetPort: ${{ values.containerPort }}
    protocol: TCP
    name: http
  selector:
    app: ${{ values.projectName }}