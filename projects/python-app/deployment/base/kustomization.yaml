apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - namespace.yaml
  - deployment.yaml
  - service.yaml
  - ingress.yaml

labels:
  - pairs:
      app.kubernetes.io/name: ${{ values.projectName }}
      app.kubernetes.io/part-of: python-web-application
    includeSelectors: true

configMapGenerator:
  - name: ${{ values.projectName }}-config
    literals:
      - APP_HOST=0.0.0.0
      - APP_PORT=${{ values.containerPort }}
      - DEBUG=false