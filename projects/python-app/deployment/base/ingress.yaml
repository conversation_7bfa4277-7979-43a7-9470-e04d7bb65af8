apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${{ values.projectName }}
  namespace: ${{ values.namespace }}
  labels:
    backstage.io/kubernetes-id: ${{ values.component_id }}
    app.kubernetes.io/name: ${{ values.component_id }}
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ${{ values.component_id }}
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ${{ values.projectName }}
            port:
              number: ${{ values.servicePort }}