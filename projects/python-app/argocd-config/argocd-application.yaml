apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.name }}-python
  namespace: ec-argocd
spec:
  project: default
  source:
    repoURL: ${{ values.pythonResourcesRepoUrl }}
    targetRevision: HEAD
    path: ${{ values.env }}/python/${{ values.name }}/overlays/${{ values.env }}
  destination:
    name: idp-test
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true