# Python App Template Conversion Guide

This document summarizes the steps taken to convert the python-app-template into a Backstage scaffolder template, and provides guidance for future updates.

## Conversion Steps Summary

1. **Directory Structure Creation**
   - Created `backstage-templates/projects/python-app/` with subdirectories:
     - `skeleton/` - Application source code
     - `deployment/base/` - Base Kubernetes manifests
     - `deployment/overlays/` - Environment-specific patches
     - `argocd-config/` - ArgoCD application configuration

2. **Template.yaml Configuration**
   - Created comprehensive parameter definitions for:
     - Basic app info (name, description, Python version)
     - Kafka configuration (cluster, topic, username)
     - Database configuration (cluster, username, password, database name)
     - Application URL configuration
     - Deployment settings (environment, replicas, ports)

3. **Skeleton Files**
   - Copied all source files from `python-app-template/` except `deploy/` folder
   - Updated HTML templates to use `${{ projectName }}` placeholder
   - Added `.gitlab-ci.yml` with TechDocs generation similar to .NET template
   - Created `catalog-info.yaml` for Backstage component registration
   - Added `mkdocs.yml` for documentation structure

4. **Deployment Manifests**
   - Base manifests: deployment.yaml, service.yaml, namespace.yaml, kustomization.yaml, secret.yaml
   - Overlay templates (*.tpl): deployment-patch.yaml.tpl, ingress-patch.yaml.tpl, kustomization.yaml.tpl
   - All manifests use template variables for dynamic values

5. **Parameter Replacements**
   - `${{ projectName }}` - Application name throughout
   - `${{ kafkaClusterName }}` - In KAFKA_BOOTSTRAP_SERVERS
   - `${{ kafkaTopic }}` - In KAFKA_TOPIC environment variable
   - `${{ kafkaUsername }}` - As secret name for Kafka certificates
   - `${{ appUrl }}` - In ingress hostname
   - Database URL constructed from db parameters
   - Container and service ports configurable

## Future Update Process

When the python-app-template is updated:

1. **Identify Changes**
   ```bash
   cd python-app-template
   git diff HEAD~1 HEAD --name-only
   ```

2. **Update Skeleton Files**
   - Copy modified files to `skeleton/` directory
   - Ensure placeholders are maintained:
     - HTML files: `${{ projectName }}`
     - Config files: appropriate template variables

3. **Update Deployment Structure**
   - If new environment variables added: update `deployment-patch.yaml.tpl`
   - If new secrets needed: update `kustomization.yaml.tpl`
   - If ingress changes: update `ingress-patch.yaml.tpl`

4. **Update Template Parameters**
   - Add new parameters to `template.yaml` if needed
   - Ensure parameter validation (patterns, defaults, enums)
   - Update step values to pass new parameters

5. **Test Template**
   - Use Backstage scaffolder to generate an app
   - Verify all placeholders are replaced correctly
   - Check that generated manifests are valid

## Key Differences from Original

1. **Dynamic Values**: All hardcoded values replaced with template parameters
2. **GitOps Integration**: Added ArgoCD application creation
3. **TechDocs**: Added documentation generation in CI pipeline
4. **Flexible Configuration**: Made Kafka, database, and deployment settings configurable
5. **Multi-Environment**: Support for deva/prod environments

## Template Variables Reference

| Variable | Usage | Default |
|----------|-------|---------|
| projectName | App name, namespace, resource names | - |
| pythonVersion | Python Docker image version | 3.12 |
| kafkaClusterName | Kafka bootstrap server prefix | kafka-cluster-python |
| kafkaTopic | Kafka topic name | - |
| kafkaUsername | Kafka cert secret name | user |
| dbClusterName | StackGres cluster name | - |
| dbUsername | Database username | - |
| dbPassword | Database password | - |
| dbName | Database name | - |
| appUrl | Ingress URL prefix | python-app |
| env | Environment (deva/prod) | deva |
| replicas | Deployment replicas | 1 |
| containerPort | Container port | 8000 |
| servicePort | Service port | 80 |