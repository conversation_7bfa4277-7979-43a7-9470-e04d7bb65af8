import pytest
from unittest.mock import Mock, patch
from app.kafka_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


@pytest.fixture
def kafka_manager():
    return KafkaManager()


@patch('app.kafka_manager.KafkaProducer')
def test_get_producer(mock_producer_class, kafka_manager):
    mock_producer = Mock()
    mock_producer_class.return_value = mock_producer
    
    producer = kafka_manager.get_producer()
    
    assert producer is not None
    mock_producer_class.assert_called_once()


@patch('app.kafka_manager.KafkaConsumer')
def test_get_consumer(mock_consumer_class, kafka_manager):
    mock_consumer = Mock()
    mock_consumer_class.return_value = mock_consumer
    
    consumer = kafka_manager.get_consumer()
    
    assert consumer is not None
    mock_consumer_class.assert_called_once()


@pytest.mark.asyncio
@patch('app.kafka_manager.KafkaProducer')
async def test_send_message(mock_producer_class, kafka_manager):
    mock_producer = Mock()
    mock_future = Mock()
    mock_producer.send.return_value = mock_future
    mock_producer_class.return_value = mock_producer
    
    result = await kafka_manager.send_message("test message")
    
    assert result is True
    mock_producer.send.assert_called_once()
    mock_producer.flush.assert_called_once()


def test_get_messages_empty(kafka_manager):
    messages = kafka_manager.get_messages()
    assert messages == []


def test_get_messages_with_data(kafka_manager):
    test_message = {"message": "test", "timestamp": "123", "offset": 0}
    kafka_manager.messages.append(test_message)
    
    messages = kafka_manager.get_messages()
    assert len(messages) == 1
    assert messages[0] == test_message