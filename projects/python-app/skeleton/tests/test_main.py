import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool
from contextlib import asynccontextmanager

# Import after conftest.py sets the environment
from app.database import Base, get_db, create_tables

# Create test database engine - always use SQLite in-memory
test_engine = create_async_engine(
    "sqlite+aiosqlite:///:memory:",
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = async_sessionmaker(
    test_engine, 
    class_=AsyncSession,
    autocommit=False, 
    autoflush=False, 
    expire_on_commit=False
)


async def override_get_db():
    async with TestingSessionLocal() as session:
        yield session


# Create a test app with lifespan that creates tables
@asynccontextmanager
async def lifespan_with_test_db(app):
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    # Cleanup
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


# Import app after setting up test config
from app.main import app

# Override the lifespan
app.router.lifespan_context = lifespan_with_test_db

# Override the dependency
app.dependency_overrides[get_db] = override_get_db


@pytest.fixture
def client():
    with TestClient(app) as c:
        yield c


def test_read_main(client):
    response = client.get("/")
    assert response.status_code == 200
    assert "Welcome to Python Web App" in response.text


def test_kafka_page(client):
    response = client.get("/kafka")
    assert response.status_code == 200
    assert "Send Message" in response.text


def test_todos_page(client):
    response = client.get("/todos")
    assert response.status_code == 200
    assert "Your Todos" in response.text


def test_add_todo(client):
    response = client.post("/todos/add", data={"title": "Test Todo"}, follow_redirects=False)
    assert response.status_code == 303
    
    response = client.get("/todos")
    assert "Test Todo" in response.text


def test_add_empty_todo(client):
    response = client.post("/todos/add", data={"title": ""})
    assert response.status_code == 422  # FastAPI returns 422 for validation errors