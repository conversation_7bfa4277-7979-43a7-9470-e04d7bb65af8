# API Reference

Complete API documentation for the Python Application Template endpoints.

## API Overview

Base URL: `http://localhost:8000`

The API provides both REST endpoints and web pages. All endpoints return JSON unless otherwise specified.

## Authentication

Currently, the API does not require authentication for basic operations. Authentication can be added based on your requirements.

## Endpoints

### Health Check Endpoints

#### GET /health

Basic health check endpoint.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes**
- `200 OK`: Service is healthy
- `503 Service Unavailable`: Service is unhealthy

#### GET /health/ready

Detailed readiness check including dependencies.

**Response**
```json
{
  "status": "ready",
  "checks": {
    "database": "ok",
    "kafka": "ok"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes**
- `200 OK`: Service is ready
- `503 Service Unavailable`: One or more dependencies are not ready

### Web Pages

#### GET /

Home page with navigation.

**Response**: HTML page

**Example**
```bash
curl http://localhost:8000/
```

#### GET /todos

Todo list management page.

**Response**: HTML page with todo list interface

#### GET /kafka

Kafka message management page.

**Response**: HTML page with Kafka interface

### Todo API Endpoints

#### GET /api/todos

Retrieve all todos.

**Query Parameters**
- `completed` (boolean, optional): Filter by completion status
- `limit` (integer, optional): Maximum number of results (default: 100)
- `offset` (integer, optional): Number of results to skip (default: 0)

**Response**
```json
{
  "todos": [
    {
      "id": 1,
      "title": "Complete documentation",
      "description": "Write API reference",
      "completed": false,
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ],
  "total": 1,
  "limit": 100,
  "offset": 0
}
```

**Example**
```bash
# Get all todos
curl http://localhost:8000/api/todos

# Get completed todos only
curl http://localhost:8000/api/todos?completed=true

# Pagination
curl http://localhost:8000/api/todos?limit=10&offset=20
```

#### GET /api/todos/{todo_id}

Retrieve a specific todo by ID.

**Path Parameters**
- `todo_id` (integer, required): Todo ID

**Response**
```json
{
  "id": 1,
  "title": "Complete documentation",
  "description": "Write API reference",
  "completed": false,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T10:00:00Z"
}
```

**Status Codes**
- `200 OK`: Todo found
- `404 Not Found`: Todo not found

#### POST /api/todos

Create a new todo.

**Request Body**
```json
{
  "title": "New todo",
  "description": "Optional description"
}
```

**Response**
```json
{
  "id": 2,
  "title": "New todo",
  "description": "Optional description",
  "completed": false,
  "created_at": "2024-01-01T11:00:00Z",
  "updated_at": "2024-01-01T11:00:00Z"
}
```

**Status Codes**
- `201 Created`: Todo created successfully
- `400 Bad Request`: Invalid input

**Example**
```bash
curl -X POST http://localhost:8000/api/todos \
  -H "Content-Type: application/json" \
  -d '{"title": "New task", "description": "Task details"}'
```

#### PUT /api/todos/{todo_id}

Update an existing todo.

**Path Parameters**
- `todo_id` (integer, required): Todo ID

**Request Body**
```json
{
  "title": "Updated title",
  "description": "Updated description",
  "completed": true
}
```

**Response**
```json
{
  "id": 1,
  "title": "Updated title",
  "description": "Updated description",
  "completed": true,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

**Status Codes**
- `200 OK`: Todo updated
- `404 Not Found`: Todo not found
- `400 Bad Request`: Invalid input

#### DELETE /api/todos/{todo_id}

Delete a todo.

**Path Parameters**
- `todo_id` (integer, required): Todo ID

**Response**
```json
{
  "message": "Todo deleted successfully"
}
```

**Status Codes**
- `200 OK`: Todo deleted
- `404 Not Found`: Todo not found

### Kafka API Endpoints

#### GET /api/kafka/messages

Retrieve stored Kafka messages.

**Query Parameters**
- `limit` (integer, optional): Maximum number of messages (default: 100)
- `topic` (string, optional): Filter by topic name

**Response**
```json
{
  "messages": [
    {
      "id": "msg-123",
      "topic": "app-events",
      "value": {"event": "test", "data": "example"},
      "timestamp": "2024-01-01T12:00:00Z",
      "partition": 0,
      "offset": 42
    }
  ],
  "total": 1
}
```

#### POST /api/kafka/send

Send a message to Kafka.

**Request Body**
```json
{
  "topic": "app-events",
  "message": {
    "event": "user.created",
    "user_id": 123,
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "key": "user-123"  // optional
}
```

**Response**
```json
{
  "status": "sent",
  "topic": "app-events",
  "partition": 0,
  "offset": 43,
  "timestamp": "2024-01-01T12:00:01Z"
}
```

**Status Codes**
- `200 OK`: Message sent successfully
- `400 Bad Request`: Invalid message format
- `503 Service Unavailable`: Kafka is not available

**Example**
```bash
curl -X POST http://localhost:8000/api/kafka/send \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "app-events",
    "message": {"event": "test", "data": "Hello Kafka"}
  }'
```

#### DELETE /api/kafka/messages/{message_id}

Delete a stored Kafka message from memory.

**Path Parameters**
- `message_id` (string, required): Message ID

**Response**
```json
{
  "message": "Message deleted successfully"
}
```

**Status Codes**
- `200 OK`: Message deleted
- `404 Not Found`: Message not found

#### POST /api/kafka/clear

Clear all stored Kafka messages from memory.

**Response**
```json
{
  "message": "All messages cleared",
  "count": 42
}
```

### Form Endpoints (Used by Web UI)

These endpoints handle form submissions from the web interface.

#### POST /todos/add

Create a todo via form submission.

**Form Data**
- `title` (string, required): Todo title
- `description` (string, optional): Todo description

**Response**: Redirect to `/todos`

#### POST /todos/delete/{todo_id}

Delete a todo via form submission.

**Response**: Redirect to `/todos`

#### POST /kafka/send

Send a Kafka message via form submission.

**Form Data**
- `message` (string, required): Message content

**Response**: Redirect to `/kafka`

## Request/Response Models

### Todo Model

```python
class Todo:
    id: int
    title: str
    description: Optional[str]
    completed: bool
    created_at: datetime
    updated_at: datetime
```

### Kafka Message Model

```python
class KafkaMessage:
    id: str
    topic: str
    value: dict
    timestamp: datetime
    partition: int
    offset: int
```

## Error Responses

All errors follow a consistent format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "title",
        "message": "Title is required"
      }
    ]
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `NOT_FOUND` | Resource not found |
| `INTERNAL_ERROR` | Internal server error |
| `SERVICE_UNAVAILABLE` | External service unavailable |
| `RATE_LIMITED` | Too many requests |

## Rate Limiting

Currently not implemented. Can be added using middleware:

```python
from slowapi import Limiter
limiter = Limiter(key_func=get_remote_address)

@app.get("/api/todos")
@limiter.limit("100/minute")
async def get_todos():
    pass
```

## CORS Configuration

CORS is configured to allow all origins in development:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## WebSocket Endpoints (Future)

WebSocket support for real-time features:

```python
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    # Handle WebSocket messages
```

## API Documentation

### Swagger UI

Interactive API documentation available at:
```
http://localhost:8000/docs
```

### ReDoc

Alternative API documentation at:
```
http://localhost:8000/redoc
```

### OpenAPI Schema

Raw OpenAPI schema available at:
```
http://localhost:8000/openapi.json
```

## Example Client Code

### Python Client

```python
import httpx
import asyncio

async def example_client():
    async with httpx.AsyncClient() as client:
        # Create a todo
        response = await client.post(
            "http://localhost:8000/api/todos",
            json={"title": "Test todo", "description": "From Python"}
        )
        todo = response.json()
        print(f"Created todo: {todo}")
        
        # Get all todos
        response = await client.get("http://localhost:8000/api/todos")
        todos = response.json()
        print(f"Total todos: {todos['total']}")

asyncio.run(example_client())
```

### JavaScript Client

```javascript
// Create a todo
fetch('http://localhost:8000/api/todos', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    title: 'Test todo',
    description: 'From JavaScript'
  })
})
.then(response => response.json())
.then(todo => console.log('Created todo:', todo));

// Get todos
fetch('http://localhost:8000/api/todos')
  .then(response => response.json())
  .then(data => console.log('Todos:', data.todos));
```

### cURL Examples

```bash
# Health check
curl http://localhost:8000/health

# Create todo
curl -X POST http://localhost:8000/api/todos \
  -H "Content-Type: application/json" \
  -d '{"title": "New todo"}'

# Send Kafka message
curl -X POST http://localhost:8000/api/kafka/send \
  -H "Content-Type: application/json" \
  -d '{"topic": "events", "message": {"test": true}}'
```

---

[← Configuration](configuration.md) | [Documentation Home](README.md) | [Database Integration →](database.md)