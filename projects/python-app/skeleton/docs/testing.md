# Testing Guide

This guide covers testing strategies, tools, and best practices for the Python Application Template.

## Testing Overview

The template includes:
- **Unit Tests**: Fast, isolated tests for individual components
- **Integration Tests**: Tests with real databases and services
- **API Tests**: HTTP endpoint testing
- **Async Tests**: Support for async/await testing
- **Coverage Reports**: Code coverage metrics

## Test Structure

```
tests/
├── __init__.py
├── conftest.py          # Shared fixtures
├── test_main.py         # API endpoint tests
├── test_database.py     # Database tests
├── test_kafka.py        # Kafka integration tests
├── unit/               # Unit tests
│   ├── test_models.py
│   └── test_utils.py
└── integration/        # Integration tests
    ├── test_api_integration.py
    └── test_kafka_integration.py
```

## Running Tests

### Basic Test Execution

```bash
# Run all tests
task test

# Run with pytest directly
pytest

# Run specific test file
pytest tests/test_main.py

# Run specific test
pytest tests/test_main.py::test_health_check

# Run tests matching pattern
pytest -k "test_create"
```

### Test Coverage

```bash
# Run with coverage
task test-coverage

# Generate HTML report
pytest --cov=app --cov-report=html

# View coverage report
open htmlcov/index.html
```

### Test Markers

```bash
# Run only unit tests
pytest -m unit

# Skip integration tests
pytest -m "not integration"

# Run only async tests
pytest -m asyncio
```

## Writing Tests

### Basic Test Structure

```python
# tests/test_example.py
import pytest
from app.main import app

def test_simple_function():
    """Test a simple function."""
    result = add(2, 3)
    assert result == 5

class TestUserAPI:
    """Group related tests."""
    
    def test_create_user(self):
        """Test user creation."""
        user = create_user("<EMAIL>")
        assert user.email == "<EMAIL>"
    
    def test_invalid_email(self):
        """Test validation."""
        with pytest.raises(ValueError):
            create_user("invalid-email")
```

### Async Tests

```python
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_async_endpoint():
    """Test async endpoint."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/health")
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_database_operation(db_session):
    """Test async database operation."""
    result = await create_todo(db_session, "Test Todo")
    assert result.title == "Test Todo"
```

### API Testing

```python
# tests/test_api.py
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_read_todos():
    """Test GET /todos endpoint."""
    response = client.get("/api/todos")
    assert response.status_code == 200
    assert "todos" in response.json()

def test_create_todo():
    """Test POST /api/todos endpoint."""
    response = client.post(
        "/api/todos",
        json={"title": "Test", "description": "Test todo"}
    )
    assert response.status_code == 201
    data = response.json()
    assert data["title"] == "Test"

def test_invalid_todo():
    """Test validation."""
    response = client.post("/api/todos", json={})
    assert response.status_code == 422
```

## Test Fixtures

### Conftest Setup

```python
# tests/conftest.py
import pytest
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from app.database import Base, get_db
from app.main import app

# Configure async tests
pytest_plugins = ('pytest_asyncio',)

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()

@pytest.fixture
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create database session for tests."""
    async with AsyncSession(test_engine) as session:
        yield session
        await session.rollback()

@pytest.fixture
async def client(db_session):
    """Create test client with database override."""
    def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as c:
        yield c
    
    app.dependency_overrides.clear()
```

### Custom Fixtures

```python
@pytest.fixture
async def sample_user(db_session):
    """Create a sample user."""
    user = User(
        email="<EMAIL>",
        username="testuser"
    )
    db_session.add(user)
    await db_session.commit()
    return user

@pytest.fixture
def mock_kafka_producer(monkeypatch):
    """Mock Kafka producer."""
    mock = Mock()
    monkeypatch.setattr("app.kafka_manager.producer", mock)
    return mock

@pytest.fixture
async def authenticated_client(client):
    """Client with authentication."""
    token = create_test_token()
    client.headers["Authorization"] = f"Bearer {token}"
    return client
```

## Database Testing

### Test Database Setup

```python
# tests/test_database.py
import pytest
from sqlalchemy import select
from app.database import Todo

@pytest.mark.asyncio
async def test_create_todo(db_session):
    """Test creating a todo."""
    todo = Todo(title="Test", description="Test description")
    db_session.add(todo)
    await db_session.commit()
    
    # Verify
    result = await db_session.execute(select(Todo))
    todos = result.scalars().all()
    assert len(todos) == 1
    assert todos[0].title == "Test"

@pytest.mark.asyncio
async def test_update_todo(db_session, sample_todo):
    """Test updating a todo."""
    sample_todo.completed = True
    await db_session.commit()
    
    # Verify
    refreshed = await db_session.get(Todo, sample_todo.id)
    assert refreshed.completed is True
```

### Transaction Testing

```python
@pytest.mark.asyncio
async def test_transaction_rollback(db_session):
    """Test transaction rollback."""
    todo = Todo(title="Test")
    db_session.add(todo)
    
    # Don't commit, should rollback
    await db_session.rollback()
    
    result = await db_session.execute(select(Todo))
    assert len(result.scalars().all()) == 0
```

## Integration Testing

### Kafka Integration Tests

```python
# tests/integration/test_kafka.py
import pytest
import asyncio
from app.kafka_manager import KafkaManager

@pytest.mark.integration
@pytest.mark.asyncio
async def test_kafka_send_receive():
    """Test sending and receiving Kafka messages."""
    manager = KafkaManager()
    await manager.start()
    
    # Send message
    test_message = {"test": "data", "timestamp": "2024-01-01"}
    await manager.send_message("test-topic", test_message)
    
    # Wait for consumption
    await asyncio.sleep(2)
    
    # Verify message received
    assert len(manager.messages) > 0
    last_message = manager.messages[-1]
    assert last_message['value'] == test_message
    
    await manager.stop()
```

### Full API Integration

```python
@pytest.mark.integration
@pytest.mark.asyncio
async def test_full_todo_workflow(client):
    """Test complete todo workflow."""
    # Create todo
    create_response = await client.post(
        "/api/todos",
        json={"title": "Integration Test"}
    )
    assert create_response.status_code == 201
    todo_id = create_response.json()["id"]
    
    # Get todo
    get_response = await client.get(f"/api/todos/{todo_id}")
    assert get_response.status_code == 200
    assert get_response.json()["title"] == "Integration Test"
    
    # Update todo
    update_response = await client.put(
        f"/api/todos/{todo_id}",
        json={"completed": True}
    )
    assert update_response.status_code == 200
    
    # Delete todo
    delete_response = await client.delete(f"/api/todos/{todo_id}")
    assert delete_response.status_code == 200
    
    # Verify deletion
    get_response = await client.get(f"/api/todos/{todo_id}")
    assert get_response.status_code == 404
```

## Mocking and Patching

### Using unittest.mock

```python
from unittest.mock import Mock, patch, AsyncMock

def test_with_mock():
    """Test with mock object."""
    mock_service = Mock()
    mock_service.get_data.return_value = {"key": "value"}
    
    result = process_data(mock_service)
    assert result == {"key": "value"}
    mock_service.get_data.assert_called_once()

@patch('app.services.external_api')
def test_with_patch(mock_api):
    """Test with patched module."""
    mock_api.fetch.return_value = {"status": "ok"}
    
    result = check_external_service()
    assert result["status"] == "ok"
```

### Async Mocking

```python
@pytest.mark.asyncio
async def test_async_mock():
    """Test with async mock."""
    mock_service = AsyncMock()
    mock_service.fetch_data.return_value = {"data": [1, 2, 3]}
    
    result = await process_async(mock_service)
    assert len(result["data"]) == 3
```

### Monkeypatch

```python
def test_with_monkeypatch(monkeypatch):
    """Test with monkeypatch."""
    def mock_get_env():
        return "test-value"
    
    monkeypatch.setattr("os.getenv", mock_get_env)
    
    from app.config import settings
    assert settings.get_env_value() == "test-value"
```

## Test Data Management

### Factories

```python
# tests/factories.py
import factory
from app.database import User, Todo

class UserFactory(factory.Factory):
    class Meta:
        model = User
    
    email = factory.Sequence(lambda n: f"user{n}@example.com")
    username = factory.Faker('user_name')
    is_active = True

class TodoFactory(factory.Factory):
    class Meta:
        model = Todo
    
    title = factory.Faker('sentence', nb_words=4)
    description = factory.Faker('text')
    completed = False

# Usage
def test_with_factory():
    users = [UserFactory() for _ in range(5)]
    assert len(users) == 5
    assert all(user.is_active for user in users)
```

### Test Data Fixtures

```python
@pytest.fixture
def sample_todos():
    """Create sample todos."""
    return [
        {"title": "Todo 1", "description": "First todo"},
        {"title": "Todo 2", "description": "Second todo"},
        {"title": "Todo 3", "description": "Third todo", "completed": True}
    ]

@pytest.fixture
async def populated_db(db_session, sample_todos):
    """Populate database with test data."""
    for todo_data in sample_todos:
        todo = Todo(**todo_data)
        db_session.add(todo)
    await db_session.commit()
```

## Performance Testing

### Load Testing

```python
# tests/performance/test_load.py
import asyncio
import time
from httpx import AsyncClient

async def make_request(client: AsyncClient, endpoint: str):
    """Make a single request."""
    start = time.time()
    response = await client.get(endpoint)
    duration = time.time() - start
    return response.status_code, duration

async def load_test(url: str, num_requests: int = 100):
    """Simple load test."""
    async with AsyncClient(base_url=url) as client:
        tasks = [
            make_request(client, "/api/todos")
            for _ in range(num_requests)
        ]
        results = await asyncio.gather(*tasks)
    
    # Analyze results
    durations = [r[1] for r in results]
    print(f"Average response time: {sum(durations)/len(durations):.3f}s")
    print(f"Min: {min(durations):.3f}s, Max: {max(durations):.3f}s")
```

### Benchmark Tests

```python
import pytest

@pytest.mark.benchmark
def test_todo_creation_performance(benchmark, db_session):
    """Benchmark todo creation."""
    def create_todos():
        todos = [Todo(title=f"Todo {i}") for i in range(100)]
        db_session.add_all(todos)
        db_session.commit()
    
    benchmark(create_todos)
```

## Test Organization

### Test Naming Conventions

```python
# Good test names
def test_create_user_with_valid_email_succeeds():
    pass

def test_create_user_with_duplicate_email_raises_error():
    pass

def test_delete_nonexistent_user_returns_404():
    pass

# Use classes for grouping
class TestUserAuthentication:
    def test_login_with_valid_credentials(self):
        pass
    
    def test_login_with_invalid_password(self):
        pass
```

### Test Documentation

```python
def test_complex_workflow():
    """
    Test the complete user registration workflow.
    
    This test verifies:
    1. User can register with valid data
    2. Confirmation email is sent
    3. User can confirm email
    4. User can login after confirmation
    """
    # Test implementation
```

## CI/CD Testing

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      
      - name: Run tests
        env:
          DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost/test
        run: |
          pytest --cov=app --cov-report=xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Best Practices

### 1. Test Isolation

```python
# Good: Each test is independent
def test_create_user(db_session):
    user = create_user(db_session, "<EMAIL>")
    assert user.email == "<EMAIL>"

def test_delete_user(db_session):
    user = create_user(db_session, "<EMAIL>")
    delete_user(db_session, user.id)
    assert get_user(db_session, user.id) is None
```

### 2. Arrange-Act-Assert

```python
def test_todo_completion():
    # Arrange
    todo = Todo(title="Test", completed=False)
    
    # Act
    todo.mark_completed()
    
    # Assert
    assert todo.completed is True
    assert todo.completed_at is not None
```

### 3. Use Descriptive Assertions

```python
# Less descriptive
assert len(todos) == 3

# More descriptive
assert len(todos) == 3, f"Expected 3 todos, got {len(todos)}"

# Using pytest assertions
assert todos == expected_todos
```

### 4. Test Edge Cases

```python
@pytest.mark.parametrize("input,expected", [
    ("", ValueError),           # Empty string
    (None, ValueError),         # None value
    ("a" * 1000, ValueError),   # Too long
    ("<EMAIL>", None),  # Valid case
])
def test_email_validation(input, expected):
    if expected:
        with pytest.raises(expected):
            validate_email(input)
    else:
        validate_email(input)  # Should not raise
```

---

[← Task Commands](tasks.md) | [Documentation Home](README.md) | [Troubleshooting →](troubleshooting.md)