# Kafka Integration Guide

This guide covers Apache Kafka integration, configuration, and usage patterns in the Python Application Template.

## Overview

The template includes a complete Kafka integration with:
- **Producer**: Send messages to Kafka topics
- **Consumer**: Background message consumption
- **Security**: Support for PLAINTEXT, SSL, and SASL
- **UI**: Web interface for testing messages

## Quick Start

### Local Development with Docker

```bash
# Start Kafka and Zookeeper
docker compose up -d kafka zookeeper

# Wait for Kafka to be ready
sleep 30

# Test the connection
task kafka-test-local

# View Kafka logs
docker compose logs -f kafka
```

### Basic Configuration

```env
# .env file
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_NAME=app-events
KAFKA_GROUP_ID=app-consumer-group
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
```

## Kafka Manager

The `KafkaManager` class handles all Kafka operations:

```python
# app/kafka_manager.py
class KafkaManager:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self.messages = []  # In-memory storage
        self.consumer_task = None
        
    async def start(self):
        """Initialize Kafka producer and consumer."""
        await self.create_producer()
        await self.start_consumer()
        
    async def stop(self):
        """Cleanup Kafka connections."""
        if self.producer:
            self.producer.close()
        if self.consumer_task:
            self.consumer_task.cancel()
```

## Producing Messages

### Send Simple Messages

```python
from app.kafka_manager import kafka_manager

# Send a simple message
await kafka_manager.send_message(
    topic="app-events",
    message={"event": "user.created", "user_id": 123}
)
```

### Send with Key

```python
# Send with partition key for ordering
await kafka_manager.send_message(
    topic="user-events",
    message={"event": "profile.updated", "user_id": 123},
    key="user-123"  # Messages with same key go to same partition
)
```

### Batch Sending

```python
# Send multiple messages efficiently
messages = [
    {"event": "page.view", "page": "/home"},
    {"event": "page.view", "page": "/products"},
    {"event": "page.view", "page": "/checkout"}
]

for msg in messages:
    await kafka_manager.send_message("analytics", msg)
```

### Producer Configuration

```python
# Advanced producer configuration
from kafka import KafkaProducer

producer = KafkaProducer(
    bootstrap_servers=settings.kafka_bootstrap_servers,
    value_serializer=lambda v: json.dumps(v).encode('utf-8'),
    key_serializer=lambda k: k.encode('utf-8') if k else None,
    
    # Performance settings
    batch_size=16384,  # Batch messages for efficiency
    linger_ms=10,      # Wait up to 10ms for batching
    compression_type='gzip',  # Compress messages
    
    # Reliability settings
    acks='all',        # Wait for all replicas
    retries=3,         # Retry failed sends
    max_in_flight_requests_per_connection=5
)
```

## Consuming Messages

### Background Consumer

The template includes an automatic background consumer:

```python
async def consume_messages(self):
    """Background task to consume Kafka messages."""
    try:
        async for msg in self.consumer:
            message_data = {
                'id': str(uuid.uuid4()),
                'topic': msg.topic,
                'value': msg.value,
                'timestamp': datetime.utcnow().isoformat(),
                'partition': msg.partition,
                'offset': msg.offset
            }
            
            # Store in memory (limited to 100 messages)
            self.messages.append(message_data)
            if len(self.messages) > 100:
                self.messages.pop(0)
                
            # Process the message
            await self.process_message(msg)
            
    except Exception as e:
        logger.error(f"Consumer error: {e}")
```

### Custom Message Processing

```python
# app/kafka_manager.py
async def process_message(self, message):
    """Process individual Kafka messages."""
    try:
        data = message.value
        event_type = data.get('event')
        
        # Route messages by type
        if event_type == 'user.created':
            await handle_user_created(data)
        elif event_type == 'order.placed':
            await handle_order_placed(data)
        else:
            logger.info(f"Unhandled event type: {event_type}")
            
    except Exception as e:
        logger.error(f"Error processing message: {e}")
        # Consider dead letter queue for failed messages
```

### Consumer Configuration

```python
# Advanced consumer configuration
from kafka import KafkaConsumer

consumer = KafkaConsumer(
    settings.kafka_topic_name,
    bootstrap_servers=settings.kafka_bootstrap_servers,
    group_id=settings.kafka_group_id,
    value_deserializer=lambda m: json.loads(m.decode('utf-8')),
    
    # Start from earliest or latest
    auto_offset_reset='earliest',
    
    # Commit settings
    enable_auto_commit=True,
    auto_commit_interval_ms=5000,
    
    # Performance settings
    max_poll_records=500,
    fetch_min_bytes=1,
    fetch_max_wait_ms=500
)
```

## Security Configuration

### PLAINTEXT (Development)

```env
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
```

### SSL/TLS Encryption

```env
KAFKA_SECURITY_PROTOCOL=SSL
KAFKA_BOOTSTRAP_SERVERS=kafka1.prod:9093,kafka2.prod:9093

# SSL certificates
KAFKA_SSL_CA_LOCATION=/path/to/ca-cert.pem
KAFKA_SSL_CERT_LOCATION=/path/to/client-cert.pem
KAFKA_SSL_KEY_LOCATION=/path/to/client-key.pem
KAFKA_SSL_KEY_PASSWORD=your-key-password
```

### SASL Authentication

```env
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_BOOTSTRAP_SERVERS=kafka1.prod:9094,kafka2.prod:9094

# SASL settings
KAFKA_SASL_MECHANISM=SCRAM-SHA-512
KAFKA_SASL_USERNAME=your-username
KAFKA_SASL_PASSWORD=your-password

# Combined with SSL
KAFKA_SSL_CA_LOCATION=/path/to/ca-cert.pem
```

### Security Implementation

```python
def get_kafka_config():
    """Get Kafka configuration with security settings."""
    config = {
        'bootstrap_servers': settings.kafka_bootstrap_servers,
        'value_serializer': lambda v: json.dumps(v).encode('utf-8'),
    }
    
    # SSL configuration
    if settings.kafka_security_protocol in ['SSL', 'SASL_SSL']:
        config.update({
            'security_protocol': settings.kafka_security_protocol,
            'ssl_cafile': settings.kafka_ssl_ca_location,
            'ssl_certfile': settings.kafka_ssl_cert_location,
            'ssl_keyfile': settings.kafka_ssl_key_location,
            'ssl_password': settings.kafka_ssl_key_password,
        })
    
    # SASL configuration
    if settings.kafka_security_protocol.startswith('SASL'):
        config.update({
            'sasl_mechanism': settings.kafka_sasl_mechanism,
            'sasl_plain_username': settings.kafka_sasl_username,
            'sasl_plain_password': settings.kafka_sasl_password,
        })
    
    return config
```

## Error Handling

### Retry Logic

```python
import asyncio
from typing import Optional

async def send_with_retry(
    topic: str, 
    message: dict, 
    max_retries: int = 3,
    backoff_factor: float = 2.0
) -> bool:
    """Send message with exponential backoff retry."""
    for attempt in range(max_retries):
        try:
            await kafka_manager.send_message(topic, message)
            return True
        except Exception as e:
            if attempt == max_retries - 1:
                logger.error(f"Failed to send after {max_retries} attempts: {e}")
                return False
            
            wait_time = backoff_factor ** attempt
            logger.warning(f"Send failed, retrying in {wait_time}s: {e}")
            await asyncio.sleep(wait_time)
    
    return False
```

### Dead Letter Queue

```python
async def process_with_dlq(message):
    """Process message with dead letter queue for failures."""
    try:
        # Process the message
        await process_message(message)
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        
        # Send to dead letter queue
        dlq_message = {
            'original_topic': message.topic,
            'original_message': message.value,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat(),
            'attempts': getattr(message, 'attempts', 1)
        }
        
        await kafka_manager.send_message(
            topic=f"{message.topic}.dlq",
            message=dlq_message
        )
```

### Connection Monitoring

```python
class KafkaHealthCheck:
    @staticmethod
    async def check_kafka_health() -> dict:
        """Check Kafka connectivity and health."""
        try:
            # Try to create a producer
            producer = KafkaProducer(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                max_block_ms=5000  # 5 second timeout
            )
            
            # Get metadata
            metadata = producer._metadata
            
            producer.close()
            
            return {
                "status": "healthy",
                "brokers": len(metadata.brokers()),
                "topics": len(metadata.topics())
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
```

## Testing Kafka

### Unit Testing

```python
# tests/test_kafka.py
import pytest
from unittest.mock import Mock, AsyncMock
from app.kafka_manager import KafkaManager

@pytest.fixture
def mock_kafka_manager():
    manager = KafkaManager()
    manager.producer = Mock()
    manager.send = AsyncMock()
    return manager

@pytest.mark.asyncio
async def test_send_message(mock_kafka_manager):
    # Test sending a message
    await mock_kafka_manager.send_message(
        "test-topic",
        {"test": "message"}
    )
    
    # Verify send was called
    mock_kafka_manager.send.assert_called_once()
```

### Integration Testing

```python
# tests/integration/test_kafka_integration.py
import pytest
from app.kafka_manager import KafkaManager

@pytest.mark.integration
@pytest.mark.asyncio
async def test_kafka_round_trip():
    """Test sending and receiving a message."""
    manager = KafkaManager()
    await manager.start()
    
    # Send message
    test_message = {"test": "integration", "id": 123}
    await manager.send_message("test-topic", test_message)
    
    # Wait for consumption
    await asyncio.sleep(2)
    
    # Verify message was received
    assert len(manager.messages) > 0
    last_message = manager.messages[-1]
    assert last_message['value'] == test_message
    
    await manager.stop()
```

### Local Testing Commands

```bash
# Test Kafka connectivity
task kafka-test-local

# Send test message via API
curl -X POST http://localhost:8000/api/kafka/send \
  -H "Content-Type: application/json" \
  -d '{"topic": "test", "message": {"hello": "kafka"}}'

# View Kafka topics
docker compose exec kafka kafka-topics --list --bootstrap-server localhost:9092

# View consumer groups
docker compose exec kafka kafka-consumer-groups --list --bootstrap-server localhost:9092

# View topic details
docker compose exec kafka kafka-topics --describe --topic app-events --bootstrap-server localhost:9092
```

## Performance Tuning

### Producer Optimization

```python
# Batch settings for high throughput
producer = KafkaProducer(
    batch_size=32768,  # 32KB batches
    linger_ms=100,     # Wait up to 100ms for batching
    compression_type='lz4',  # Fast compression
    buffer_memory=33554432,  # 32MB buffer
)

# For low latency
producer = KafkaProducer(
    batch_size=0,      # No batching
    linger_ms=0,       # Send immediately
    compression_type=None,  # No compression
    acks=1,            # Leader acknowledgment only
)
```

### Consumer Optimization

```python
# For high throughput
consumer = KafkaConsumer(
    max_poll_records=1000,   # Get more records per poll
    fetch_min_bytes=1024,    # Wait for 1KB minimum
    fetch_max_wait_ms=1000,  # Or 1 second
)

# For low latency
consumer = KafkaConsumer(
    max_poll_records=1,      # One record at a time
    fetch_min_bytes=1,       # Don't wait for data
    fetch_max_wait_ms=0,     # Poll immediately
)
```

## Monitoring and Metrics

### Kafka Metrics

```python
class KafkaMetrics:
    def __init__(self):
        self.messages_sent = 0
        self.messages_received = 0
        self.errors = 0
        
    async def record_send(self, topic: str, success: bool):
        if success:
            self.messages_sent += 1
        else:
            self.errors += 1
            
    def get_metrics(self):
        return {
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "errors": self.errors,
            "error_rate": self.errors / max(self.messages_sent, 1)
        }
```

### Logging

```python
import logging

# Configure Kafka logging
logging.getLogger('kafka').setLevel(logging.WARNING)
logging.getLogger('kafka.conn').setLevel(logging.WARNING)

# Custom Kafka logger
kafka_logger = logging.getLogger('app.kafka')
kafka_logger.setLevel(logging.INFO)
```

## Common Patterns

### Event Sourcing

```python
# Event structure
class Event:
    def __init__(self, event_type: str, aggregate_id: str, data: dict):
        self.id = str(uuid.uuid4())
        self.type = event_type
        self.aggregate_id = aggregate_id
        self.data = data
        self.timestamp = datetime.utcnow()
        self.version = 1

# Send events
async def emit_event(event: Event):
    await kafka_manager.send_message(
        topic="events",
        message=event.__dict__,
        key=event.aggregate_id  # Ensure ordering per aggregate
    )
```

### Request-Reply Pattern

```python
async def request_reply(request_topic: str, reply_topic: str, request: dict):
    """Send request and wait for reply."""
    correlation_id = str(uuid.uuid4())
    request['correlation_id'] = correlation_id
    
    # Subscribe to reply topic
    reply_consumer = KafkaConsumer(
        reply_topic,
        bootstrap_servers=settings.kafka_bootstrap_servers,
        value_deserializer=lambda m: json.loads(m.decode('utf-8'))
    )
    
    # Send request
    await kafka_manager.send_message(request_topic, request)
    
    # Wait for reply
    timeout = time.time() + 30  # 30 second timeout
    for message in reply_consumer:
        if message.value.get('correlation_id') == correlation_id:
            reply_consumer.close()
            return message.value
        
        if time.time() > timeout:
            reply_consumer.close()
            raise TimeoutError("Reply timeout")
```

## Troubleshooting

### Connection Issues

```bash
# Test connectivity
telnet localhost 9092

# Check Kafka logs
docker compose logs kafka

# Verify listeners
docker compose exec kafka kafka-configs --describe --bootstrap-server localhost:9092 --entity-type brokers
```

### Message Not Being Consumed

1. Check consumer group:
```bash
docker compose exec kafka kafka-consumer-groups --describe --group app-consumer-group --bootstrap-server localhost:9092
```

2. Reset consumer offset:
```bash
docker compose exec kafka kafka-consumer-groups --reset-offsets --group app-consumer-group --topic app-events --to-earliest --execute --bootstrap-server localhost:9092
```

### Performance Issues

1. Check partition count:
```bash
docker compose exec kafka kafka-topics --describe --topic app-events --bootstrap-server localhost:9092
```

2. Add more partitions:
```bash
docker compose exec kafka kafka-topics --alter --topic app-events --partitions 10 --bootstrap-server localhost:9092
```

---

[← Database Integration](database.md) | [Documentation Home](README.md) | [Web Interface →](web-interface.md)