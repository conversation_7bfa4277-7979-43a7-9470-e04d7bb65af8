# Architecture Overview

This document provides a comprehensive overview of the Python Application Template architecture, design patterns, and technical decisions.

## High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│   Web Browser   │────▶│    FastAPI      │────▶│   PostgreSQL    │
│                 │     │   Application   │     │    Database     │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │   Apache Kafka  │
                        │  Message Queue  │
                        └─────────────────┘
```

## Core Components

### 1. FastAPI Application Layer

The application is built on FastAPI, a modern, high-performance web framework:

- **Async/Await**: Full asynchronous support for high concurrency
- **Type Hints**: Automatic validation and documentation via Pydantic
- **Auto Documentation**: Built-in Swagger UI and ReDoc
- **WebSocket Support**: Real-time communication capabilities

```python
# Core application structure
app = FastAPI(
    title="Python App Template",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)
```

### 2. Database Layer

PostgreSQL with async SQLAlchemy ORM:

- **Async SQLAlchemy 2.0**: Modern async ORM patterns
- **Connection Pooling**: Efficient database connections
- **Auto-migration**: Schema creation on startup (dev mode)
- **Transaction Management**: Automatic rollback on errors

```python
# Database architecture
AsyncEngine → AsyncSession → ORM Models → Database
```

### 3. Message Queue Integration

Apache Kafka for event-driven architecture:

- **Producer/Consumer Pattern**: Decoupled message processing
- **Background Consumer**: Automatic message consumption
- **Security Options**: PLAINTEXT, SSL, SASL_SSL support
- **Error Handling**: Retry logic and dead letter queues

### 4. Web Interface

Server-side rendered templates with Jinja2:

- **Template Inheritance**: Base template for consistent UI
- **Static Assets**: Organized CSS/JS/images
- **Bootstrap 5**: Responsive design out of the box
- **HTMX Ready**: Progressive enhancement capabilities

## Project Structure

```
python-app-template/
├── app/
│   ├── __init__.py          # Package initialization
│   ├── main.py              # FastAPI app & routes
│   ├── config.py            # Configuration management
│   ├── database.py          # Database models & connection
│   ├── kafka_manager.py     # Kafka integration
│   ├── static/              # Static files (CSS, JS)
│   ├── templates/           # Jinja2 templates
│   └── middleware/          # Custom middleware (future)
├── tests/
│   ├── conftest.py          # Pytest fixtures
│   ├── test_main.py         # API tests
│   ├── test_kafka.py        # Kafka tests
│   └── test_database.py     # Database tests
├── deploy/
│   ├── base/                # Base Kubernetes manifests
│   └── overlays/            # Environment-specific configs
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container definition
├── docker-compose.yml      # Local development stack
└── Taskfile.yml           # Task automation
```

## Design Patterns

### 1. Dependency Injection

FastAPI's dependency injection system for clean, testable code:

```python
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        yield session

@app.post("/items")
async def create_item(item: ItemCreate, db: AsyncSession = Depends(get_db)):
    # db is automatically injected
    return await create_item_in_db(db, item)
```

### 2. Repository Pattern

Separation of data access logic:

```python
# repositories/item_repository.py
class ItemRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_all(self) -> List[Item]:
        result = await self.db.execute(select(Item))
        return result.scalars().all()
    
    async def create(self, item_data: dict) -> Item:
        item = Item(**item_data)
        self.db.add(item)
        await self.db.commit()
        return item
```

### 3. Service Layer

Business logic separated from routes:

```python
# services/item_service.py
class ItemService:
    def __init__(self, repo: ItemRepository):
        self.repo = repo
    
    async def create_item_with_validation(self, data: ItemCreate) -> Item:
        # Business logic here
        if await self.item_exists(data.name):
            raise ValueError("Item already exists")
        return await self.repo.create(data.dict())
```

### 4. Configuration as Code

Environment-based configuration with Pydantic:

```python
class Settings(BaseSettings):
    app_name: str = "Python App Template"
    debug: bool = False
    database_url: str
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

## Security Architecture

### Authentication & Authorization

- **API Key Authentication**: Simple token-based auth
- **JWT Support**: Stateless authentication ready
- **CORS Configuration**: Controlled cross-origin access
- **Rate Limiting**: Prevent abuse (configurable)

### Data Security

- **SQL Injection Protection**: Parameterized queries via ORM
- **Input Validation**: Pydantic models validate all inputs
- **XSS Protection**: Template auto-escaping
- **HTTPS Ready**: SSL/TLS support in production

### Kafka Security

- **SASL/SCRAM**: Username/password authentication
- **SSL/TLS**: Encrypted communication
- **ACLs**: Topic-level access control
- **Consumer Groups**: Isolated message processing

## Scalability Considerations

### Horizontal Scaling

The application is designed for horizontal scaling:

1. **Stateless Design**: No server-side session storage
2. **Database Pooling**: Shared connection pool
3. **Kafka Partitions**: Parallel message processing
4. **Load Balancer Ready**: Health check endpoints

### Performance Optimizations

1. **Async I/O**: Non-blocking database and API calls
2. **Connection Pooling**: Reuse database connections
3. **Caching Ready**: Redis integration points
4. **Lazy Loading**: On-demand resource loading

### Monitoring & Observability

Built-in support for monitoring:

```python
# Health check endpoints
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/health/ready")
async def readiness_check():
    # Check database, Kafka, etc.
    return {"status": "ready", "checks": {...}}
```

## Database Design

### Schema Design Principles

1. **Normalized Structure**: Avoid data duplication
2. **Indexes**: On frequently queried columns
3. **Constraints**: Foreign keys and unique constraints
4. **Audit Fields**: created_at, updated_at on all tables

### Example Schema

```sql
-- Todos table (example)
CREATE TABLE todos (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_created_at ON todos(created_at);
```

## Kafka Architecture

### Message Flow

```
Producer → Topic → Partitions → Consumer Groups → Consumers
```

### Message Schema

```json
{
  "id": "uuid",
  "timestamp": "2024-01-01T00:00:00Z",
  "event_type": "user.created",
  "payload": {
    "user_id": 123,
    "email": "<EMAIL>"
  },
  "metadata": {
    "source": "web-app",
    "version": "1.0"
  }
}
```

### Error Handling Strategy

1. **Retry Logic**: Exponential backoff for transient errors
2. **Dead Letter Queue**: Failed messages after max retries
3. **Idempotency**: Handle duplicate messages gracefully
4. **Monitoring**: Log all failures for analysis

## Deployment Architecture

### Container Strategy

Multi-stage Dockerfile for optimal image size:

```dockerfile
# Build stage
FROM python:3.12-slim as builder
# Install dependencies

# Runtime stage
FROM python:3.12-slim
# Copy only necessary files
```

### Kubernetes Architecture

```
Namespace
├── Deployment (Pods)
├── Service (Load Balancer)
├── ConfigMap (Configuration)
├── Secret (Sensitive Data)
├── Ingress (External Access)
└── HPA (Auto-scaling)
```

### Health Checks

```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health/ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
```

## Development vs Production

### Development Mode

- **Hot Reload**: Automatic code reloading
- **Debug Logging**: Verbose output
- **SQLite Support**: No external dependencies
- **Exposed Endpoints**: Swagger UI enabled

### Production Mode

- **Optimized Build**: Minimal container size
- **Security Hardening**: Limited exposed endpoints
- **Performance Tuning**: Connection pooling, caching
- **Monitoring**: Metrics and logging integration

## Technology Stack Summary

| Component | Technology | Purpose |
|-----------|------------|---------|
| Web Framework | FastAPI | High-performance async API |
| ASGI Server | Uvicorn | Production-grade ASGI server |
| Database | PostgreSQL | Reliable relational database |
| ORM | SQLAlchemy 2.0 | Async database operations |
| Message Queue | Apache Kafka | Event streaming platform |
| Template Engine | Jinja2 | Server-side rendering |
| UI Framework | Bootstrap 5 | Responsive design |
| Container | Docker | Application packaging |
| Orchestration | Kubernetes | Container orchestration |
| Task Runner | Task (go-task) | Development automation |

## Future Architecture Considerations

### Potential Enhancements

1. **Caching Layer**: Redis for performance
2. **API Gateway**: Kong or Traefik
3. **Service Mesh**: Istio for microservices
4. **Event Sourcing**: Full audit trail
5. **GraphQL**: Alternative API layer
6. **WebSockets**: Real-time features

### Microservices Migration Path

The template is designed to easily split into microservices:

1. **API Service**: REST endpoints
2. **Worker Service**: Kafka consumers
3. **Web Service**: UI and templates
4. **Auth Service**: Authentication/authorization

---

[← Development Guide](development.md) | [Documentation Home](README.md) | [Configuration →](configuration.md)