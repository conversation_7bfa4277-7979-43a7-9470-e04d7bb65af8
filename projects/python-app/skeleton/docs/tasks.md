# Task Commands Reference

This guide provides a complete reference for all available task commands in the Python Application Template.

## Overview

The template uses [Task](https://taskfile.dev/) for automation. Task is a modern alternative to Make that uses a simple YAML syntax.

## Installation

```bash
# macOS
brew install go-task

# Linux
sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d

# Verify installation
task --version
```

## Available Tasks

### Viewing All Tasks

```bash
# List all available tasks
task --list

# List with descriptions
task --list-all
```

## Task Categories

### 🚀 Application Tasks

#### `task run`
Runs the application locally with hot-reload enabled.

```bash
task run
# Equivalent to: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Environment Variables:**
- `APP_PORT`: Override default port (default: 8000)
- `APP_HOST`: Override default host (default: 0.0.0.0)

#### `task run-prod`
Runs the application in production mode without reload.

```bash
task run-prod
# Equivalent to: uvicorn app.main:app --host 0.0.0.0 --port 8000
```

#### `task run-hybrid`
Runs the application with Docker services but local Python code.

```bash
task run-hybrid
# Starts PostgreSQL and Kafka in Docker, runs app locally
```

### 📦 Installation Tasks

#### `task install`
Installs Python dependencies.

```bash
task install
# Equivalent to: pip install -r requirements.txt
```

#### `task install-dev`
Installs development dependencies.

```bash
task install-dev
# Equivalent to: pip install -r requirements-dev.txt
```

#### `task update-deps`
Updates all dependencies to latest versions.

```bash
task update-deps
# Updates requirements.txt with latest versions
```

### 🧪 Testing Tasks

#### `task test`
Runs all tests using pytest.

```bash
task test
# Equivalent to: pytest -v
```

**Options:**
```bash
# Run specific test file
task test -- tests/test_main.py

# Run with specific marker
task test -- -m "not integration"
```

#### `task test-coverage`
Runs tests with coverage report.

```bash
task test-coverage
# Equivalent to: pytest --cov=app --cov-report=html --cov-report=term
```

**Output:**
- Terminal coverage summary
- HTML report in `htmlcov/index.html`

#### `task test-watch`
Runs tests in watch mode.

```bash
task test-watch
# Automatically reruns tests when files change
```

### 🔍 Code Quality Tasks

#### `task lint`
Runs linting checks with flake8.

```bash
task lint
# Equivalent to: flake8 app/ tests/
```

**Configuration:** `.flake8` file

#### `task format`
Formats code using black.

```bash
task format
# Equivalent to: black app/ tests/
```

#### `task type-check`
Runs type checking with mypy.

```bash
task type-check
# Equivalent to: mypy app/
```

#### `task quality`
Runs all quality checks (lint, format check, type check).

```bash
task quality
# Runs: lint, format --check, type-check
```

### 🐳 Docker Tasks

#### `task docker-build`
Builds Docker image locally.

```bash
task docker-build
# Builds: python-app:latest

# With custom tag
DOCKER_TAG=v1.2.3 task docker-build
```

#### `task docker-build-push`
Builds and pushes image to registry.

```bash
task docker-build-push
# Pushes to: escdev.azurecr.io/python-app:latest

# With custom registry
DOCKER_REGISTRY=myregistry.com task docker-build-push
```

#### `task docker-run`
Runs the application in Docker container.

```bash
task docker-run
# Runs container with port 8000 exposed
```

#### `task docker-shell`
Opens shell in Docker container.

```bash
task docker-shell
# Interactive bash session in container
```

### 🚢 Docker Compose Tasks

#### `task compose-up`
Starts all services with docker-compose.

```bash
task compose-up
# Starts: app, postgres, kafka, zookeeper
```

#### `task compose-down`
Stops all services.

```bash
task compose-down
# Stops and removes containers
```

#### `task compose-logs`
Shows logs from all services.

```bash
task compose-logs
# Follow logs: task compose-logs -- -f
```

#### `task compose-ps`
Shows status of all services.

```bash
task compose-ps
# Lists all containers and their status
```

### ☸️ Kubernetes Tasks

#### `task k8s-deploy`
Deploys application to Kubernetes.

```bash
task k8s-deploy
# Deploys to current context

# Deploy to specific environment
ENV=prod task k8s-deploy
```

#### `task k8s-delete`
Removes application from Kubernetes.

```bash
task k8s-delete
# Deletes all resources
```

#### `task k8s-logs`
Shows application logs from Kubernetes.

```bash
task k8s-logs
# Follow logs: task k8s-logs -- -f
```

#### `task k8s-status`
Shows deployment status.

```bash
task k8s-status
# Shows pods, services, ingress status
```

#### `task k8s-port-forward`
Sets up port forwarding to pod.

```bash
task k8s-port-forward
# Forwards localhost:8000 to pod:8000
```

#### `task k8s-shell`
Opens shell in running pod.

```bash
task k8s-shell
# Interactive bash session in pod
```

### 📊 Database Tasks

#### `task db-create`
Creates database tables.

```bash
task db-create
# Runs SQLAlchemy create_all()
```

#### `task db-migrate`
Runs database migrations.

```bash
task db-migrate
# Applies Alembic migrations
```

#### `task db-rollback`
Rolls back last migration.

```bash
task db-rollback
# Reverts one migration
```

#### `task db-shell`
Opens database shell.

```bash
task db-shell
# PostgreSQL: psql connection
# SQLite: sqlite3 connection
```

### 📨 Kafka Tasks

#### `task kafka-create-topic`
Creates Kafka topic.

```bash
task kafka-create-topic
# Creates topic with default settings

# Custom topic
KAFKA_TOPIC=my-events task kafka-create-topic
```

#### `task kafka-list-topics`
Lists all Kafka topics.

```bash
task kafka-list-topics
# Shows all topics in cluster
```

#### `task kafka-test-local`
Tests local Kafka connection.

```bash
task kafka-test-local
# Sends test message to verify connectivity
```

#### `task kafka-consumer-groups`
Lists consumer groups.

```bash
task kafka-consumer-groups
# Shows all consumer groups and lag
```

### 🔐 Secret Management Tasks

#### `task k8s-create-secret`
Creates Kubernetes secret.

```bash
task k8s-create-secret
# Creates secret from .env file
```

#### `task k8s-copy-kafka-secret`
Copies Kafka certificates to Kubernetes.

```bash
task k8s-copy-kafka-secret
# Copies from local files to k8s secret
```

### 🧹 Cleanup Tasks

#### `task clean`
Cleans temporary files and caches.

```bash
task clean
# Removes: __pycache__, .pytest_cache, .coverage
```

#### `task clean-docker`
Removes Docker images and volumes.

```bash
task clean-docker
# Removes: containers, images, volumes
```

#### `task clean-all`
Complete cleanup of everything.

```bash
task clean-all
# Runs: clean, clean-docker, compose-down
```

## Task Configuration

### Environment Variables

Tasks respect these environment variables:

```bash
# Application
APP_NAME=my-app
APP_PORT=8000
DEBUG=true

# Docker
DOCKER_REGISTRY=myregistry.com
DOCKER_TAG=v1.0.0

# Kubernetes  
KUBE_NAMESPACE=production
ENV=prod

# Database
DATABASE_URL=postgresql://...

# Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
```

### Task Variables

Define in `Taskfile.yml`:

```yaml
vars:
  APP_NAME: python-app
  DOCKER_REGISTRY: escdev.azurecr.io
  DEFAULT_PORT: 8000
```

## Custom Tasks

### Adding New Tasks

Edit `Taskfile.yml`:

```yaml
tasks:
  my-task:
    desc: "Description of my task"
    cmds:
      - echo "Running my task"
      - python my_script.py
    env:
      MY_VAR: "value"
    deps: [install]  # Dependencies
```

### Task Dependencies

```yaml
tasks:
  deploy:
    desc: "Build and deploy"
    deps: [test, docker-build]
    cmds:
      - task k8s-deploy
```

### Conditional Tasks

```yaml
tasks:
  deploy-prod:
    desc: "Deploy to production"
    preconditions:
      - sh: '[[ "$ENV" == "prod" ]]'
        msg: "ENV must be set to prod"
    cmds:
      - kubectl apply -k deploy/prod
```

## Task Aliases

Common shortcuts:

```bash
# Instead of long commands
task run                # uvicorn app.main:app --reload
task test              # pytest -v
task fmt               # black app/ tests/
task dc-up             # docker-compose up -d
task k8s               # kubectl
```

## Troubleshooting

### Task Not Found

```bash
# Ensure Task is installed
which task

# Check you're in correct directory
ls Taskfile.yml
```

### Permission Errors

```bash
# Some tasks may need sudo
sudo task docker-build

# Or add user to docker group
sudo usermod -aG docker $USER
```

### Environment Issues

```bash
# Check task environment
task --summary my-task

# Run with debug
task --verbose my-task
```

## Best Practices

### 1. Use Task for Everything

```bash
# Don't memorize commands
task run          # Instead of: uvicorn app.main:app --reload
task test         # Instead of: pytest -v --cov=app
```

### 2. Chain Tasks

```bash
# Run multiple tasks
task clean test docker-build

# Or define composite task
task ci  # Runs: lint, test, build
```

### 3. Document Tasks

```yaml
tasks:
  complex-task:
    desc: "Does something complex"
    summary: |
      This task performs the following:
      1. Validates environment
      2. Runs migrations
      3. Deploys application
```

### 4. Use Environment Files

```bash
# Development
task --env-file .env.dev run

# Production
task --env-file .env.prod deploy
```

---

[← GitOps Integration](gitops.md) | [Documentation Home](README.md) | [Testing Guide →](testing.md)