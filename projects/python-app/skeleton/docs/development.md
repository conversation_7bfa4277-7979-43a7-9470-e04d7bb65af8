# Development Guide

This guide covers the development workflow, best practices, and common patterns for working with the Python Application Template.

## Development Workflow

### 1. Setting Up Your Development Environment

```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies in development mode
task install

# Start dependencies
docker compose up -d

# Run the application with hot-reload
task run
```

### 2. Code Organization

```
python-app-template/
├── app/                    # Application code
│   ├── __init__.py        # Package initialization
│   ├── main.py            # FastAPI application and routes
│   ├── config.py          # Configuration management
│   ├── database.py        # Database models and connection
│   ├── kafka_manager.py   # Kafka producer/consumer logic
│   ├── static/            # Static assets (CSS, JS, images)
│   └── templates/         # Jinja2 HTML templates
├── tests/                 # Test files
├── deploy/                # Kubernetes manifests
└── requirements.txt       # Python dependencies
```

### 3. Development Commands

The most frequently used development commands:

```bash
# Run application with hot-reload
task run

# Run tests
task test

# Run tests with coverage
task test-coverage

# Format code
task format

# Lint code
task lint

# Run type checks
task type-check
```

## Working with FastAPI

### Adding New Routes

Create new endpoints in `app/main.py`:

```python
@app.get("/api/items")
async def get_items():
    """Get all items."""
    return {"items": ["item1", "item2"]}

@app.post("/api/items")
async def create_item(item: dict):
    """Create a new item."""
    # Add your logic here
    return {"status": "created", "item": item}
```

### Adding Request Models

Use Pydantic models for request/response validation:

```python
from pydantic import BaseModel

class ItemCreate(BaseModel):
    name: str
    description: str | None = None
    price: float

@app.post("/api/items", response_model=ItemCreate)
async def create_item(item: ItemCreate):
    return item
```

### Working with Templates

Add new HTML templates in `app/templates/`:

```python
from fastapi import Request
from fastapi.templating import Jinja2Templates

templates = Jinja2Templates(directory="app/templates")

@app.get("/new-page")
async def new_page(request: Request):
    return templates.TemplateResponse(
        "new_page.html",
        {"request": request, "title": "New Page"}
    )
```

## Database Development

### Adding New Models

Define models in `app/database.py`:

```python
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func

class Item(Base):
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
```

### Database Operations

Use async SQLAlchemy for database operations:

```python
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

async def get_items(db: AsyncSession):
    result = await db.execute(select(Item))
    return result.scalars().all()

async def create_item(db: AsyncSession, name: str, description: str):
    item = Item(name=name, description=description)
    db.add(item)
    await db.commit()
    await db.refresh(item)
    return item
```

### Running Database Migrations

For development, tables are created automatically. For production, use Alembic:

```bash
# Initialize Alembic (one-time)
alembic init alembic

# Create a migration
alembic revision --autogenerate -m "Add items table"

# Apply migrations
alembic upgrade head
```

## Working with Kafka

### Producing Messages

Send messages using the KafkaManager:

```python
from app.kafka_manager import kafka_manager

# Send a message
await kafka_manager.send_message("user-events", {
    "event": "user_created",
    "user_id": 123,
    "timestamp": datetime.utcnow().isoformat()
})
```

### Consuming Messages

The Kafka consumer runs automatically in the background. To process messages:

```python
# In app/kafka_manager.py
async def process_message(message):
    """Process incoming Kafka messages."""
    try:
        data = json.loads(message.value)
        # Add your message processing logic here
        logger.info(f"Processed message: {data}")
    except Exception as e:
        logger.error(f"Error processing message: {e}")
```

### Testing Kafka Integration

```bash
# Test local Kafka connection
task kafka-test-local

# Send a test message
curl -X POST http://localhost:8000/kafka/send \
  -H "Content-Type: application/json" \
  -d '{"message": "Test message"}'
```

## Configuration Management

### Environment Variables

Use the `app/config.py` for centralized configuration:

```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Add new settings
    my_custom_setting: str = "default_value"
    api_timeout: int = 30
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### Using Configuration

```python
from app.config import settings

# Use in your code
timeout = settings.api_timeout
custom_value = settings.my_custom_setting
```

## Testing Best Practices

### Writing Unit Tests

Create test files in the `tests/` directory:

```python
# tests/test_items.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_item():
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/api/items",
            json={"name": "Test Item", "price": 9.99}
        )
    assert response.status_code == 200
    assert response.json()["name"] == "Test Item"
```

### Testing Database Operations

```python
# tests/test_database.py
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db, Item

@pytest.mark.asyncio
async def test_create_item(db_session: AsyncSession):
    # Create item
    item = Item(name="Test", description="Test item")
    db_session.add(item)
    await db_session.commit()
    
    # Verify
    assert item.id is not None
    assert item.name == "Test"
```

### Running Tests

```bash
# Run all tests
task test

# Run specific test file
pytest tests/test_items.py

# Run with coverage
task test-coverage

# Run tests in watch mode
pytest-watch
```

## Debugging Tips

### Enable Debug Logging

```python
# In .env or environment
DEBUG=true
LOG_LEVEL=DEBUG

# In code
import logging
logger = logging.getLogger(__name__)
logger.debug("Debug information here")
```

### Using VS Code Debugger

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "app.main:app",
        "--reload",
        "--port", "8000"
      ],
      "env": {
        "DEBUG": "true"
      }
    }
  ]
}
```

### Common Debugging Commands

```bash
# Check application logs
task logs

# Interactive Python shell with app context
python -i -c "from app.main import app; from app.database import *"

# Database shell
docker compose exec postgres psql -U postgres -d app_db
```

## Performance Optimization

### Async Best Practices

```python
# Good: Concurrent operations
async def get_data():
    results = await asyncio.gather(
        fetch_from_database(),
        fetch_from_api(),
        fetch_from_cache()
    )
    return results

# Bad: Sequential operations
async def get_data():
    db_data = await fetch_from_database()
    api_data = await fetch_from_api()
    cache_data = await fetch_from_cache()
    return [db_data, api_data, cache_data]
```

### Database Query Optimization

```python
# Use select with specific columns
from sqlalchemy import select

# Good: Select only needed columns
stmt = select(Item.id, Item.name).where(Item.active == True)

# Use eager loading for relationships
stmt = select(Item).options(selectinload(Item.tags))
```

### Caching

```python
from functools import lru_cache
from app.config import settings

@lru_cache()
def get_expensive_config():
    """Cache expensive configuration loading."""
    return process_config(settings)
```

## Code Style Guidelines

### Follow PEP 8

```bash
# Format code with black
black app/ tests/

# Check with flake8
flake8 app/ tests/

# Type checking with mypy
mypy app/
```

### Project Conventions

1. **Async First**: Use async/await for all I/O operations
2. **Type Hints**: Add type hints to all functions
3. **Docstrings**: Document all public functions and classes
4. **Error Handling**: Use proper exception handling
5. **Logging**: Use structured logging for debugging

### Example of Good Code

```python
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging

logger = logging.getLogger(__name__)

async def get_active_items(
    db: AsyncSession,
    limit: int = 100,
    offset: int = 0
) -> List[Item]:
    """
    Retrieve active items from the database.
    
    Args:
        db: Database session
        limit: Maximum number of items to return
        offset: Number of items to skip
        
    Returns:
        List of active items
    """
    try:
        stmt = select(Item).where(
            Item.active == True
        ).limit(limit).offset(offset)
        
        result = await db.execute(stmt)
        items = result.scalars().all()
        
        logger.info(f"Retrieved {len(items)} active items")
        return items
        
    except Exception as e:
        logger.error(f"Error retrieving items: {e}")
        raise
```

## Hot Reload and Development Speed

### Automatic Reload

The application automatically reloads when you change Python files:

```bash
# Run with hot-reload enabled (default in development)
task run

# You'll see in logs:
# INFO:     Will watch for changes in these directories: ['/path/to/app']
```

### Speeding Up Development

1. **Use Task Commands**: Predefined tasks save typing
2. **Docker Compose Profiles**: Run only needed services
3. **SQLite for Testing**: Faster than PostgreSQL for unit tests
4. **Concurrent Testing**: Use `pytest-xdist` for parallel tests

```bash
# Run tests in parallel
pytest -n auto

# Run only specific service
docker compose up -d postgres  # Skip Kafka if not needed
```

## Next Steps

- Review [Architecture Overview](architecture.md) for design patterns
- Check [Testing Guide](testing.md) for comprehensive testing
- See [API Reference](api-reference.md) for endpoint documentation
- Explore [Deployment Guide](kubernetes.md) for production setup

---

[← Quick Start](quickstart.md) | [Documentation Home](README.md) | [Architecture →](architecture.md)