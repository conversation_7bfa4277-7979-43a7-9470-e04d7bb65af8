# GitOps Integration Guide

This guide covers integrating the Python Application Template with GitOps workflows, particularly with ArgoCD and Backstage.

## Overview

The template is designed for GitOps workflows:
- **Declarative configuration**: All resources defined in Git
- **Automated deployment**: ArgoCD monitors and syncs changes
- **Self-service**: Backstage scaffolder integration
- **Environment promotion**: Dev → Staging → Production

## GitOps Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Backstage     │────▶│   Git Repo      │◀────│    ArgoCD       │
│   Scaffolder    │     │  (Manifests)    │     │   Controller    │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                          │
                                                          ▼
                                                 ┌─────────────────┐
                                                 │   Kubernetes    │
                                                 │    Cluster      │
                                                 └─────────────────┘
```

## Repository Structure

### Application Repository

```
python-app/
├── .github/
│   └── workflows/
│       ├── build.yml       # CI pipeline
│       └── release.yml     # Release automation
├── app/                    # Application code
├── deploy/                 # Kubernetes manifests
│   ├── base/
│   └── overlays/
├── Dockerfile
└── backstage-catalog.yaml  # Backstage component
```

### GitOps Repository

```
gitops-manifests/
├── apps/
│   ├── dev/
│   │   └── python-app/
│   │       └── kustomization.yaml
│   ├── staging/
│   │   └── python-app/
│   │       └── kustomization.yaml
│   └── prod/
│       └── python-app/
│           └── kustomization.yaml
└── argocd/
    └── applications/
        ├── python-app-dev.yaml
        ├── python-app-staging.yaml
        └── python-app-prod.yaml
```

## ArgoCD Configuration

### Application Definition

```yaml
# argocd/applications/python-app-dev.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: python-app-dev
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://git.example.com/apps/python-app
    targetRevision: develop
    path: deploy/overlays/dev
  destination:
    server: https://kubernetes.default.svc
    namespace: dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
    - CreateNamespace=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
```

### Multi-Environment Setup

```yaml
# argocd/applications/python-app-prod.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: python-app-prod
  namespace: argocd
spec:
  project: production
  source:
    repoURL: https://git.example.com/apps/python-app
    targetRevision: v1.0.0  # Use tags for production
    path: deploy/overlays/prod
  destination:
    server: https://prod-cluster.example.com
    namespace: production
  syncPolicy:
    automated: false  # Manual sync for production
    syncOptions:
    - CreateNamespace=true
    - PruneLast=true
```

### App of Apps Pattern

```yaml
# argocd/app-of-apps.yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: applications
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://git.example.com/gitops
    targetRevision: HEAD
    path: argocd/applications
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

## Backstage Integration

### Component Definition

```yaml
# backstage-catalog.yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: python-app
  description: Python Application Template
  tags:
    - python
    - fastapi
    - kafka
  links:
    - url: https://argocd.example.com/applications/python-app-dev
      title: ArgoCD Dev
    - url: https://grafana.example.com/d/python-app
      title: Grafana Dashboard
  annotations:
    github.com/project-slug: myorg/python-app
    argocd/app-name: python-app-dev
spec:
  type: service
  lifecycle: production
  owner: platform-team
  system: internal-tools
  dependsOn:
    - resource:default/postgres-db
    - resource:default/kafka-cluster
```

### Scaffolder Template

```yaml
# backstage-templates/python-app/template.yaml
apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: python-app-template
  title: Python Application
  description: Create a new Python FastAPI application
spec:
  owner: platform-team
  type: service
  parameters:
    - title: Application Details
      required:
        - name
        - description
      properties:
        name:
          title: Name
          type: string
          pattern: '^[a-z0-9-]+$'
        description:
          title: Description
          type: string
    - title: Infrastructure
      properties:
        kafka:
          title: Enable Kafka
          type: boolean
          default: true
        database:
          title: Database Type
          type: string
          enum:
            - postgresql
            - mysql
          default: postgresql
  steps:
    - id: fetch
      name: Fetch Template
      action: fetch:template
      input:
        url: ./skeleton
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
    
    - id: publish
      name: Publish to GitHub
      action: publish:github
      input:
        repoUrl: github.com?owner=myorg&repo=${{ parameters.name }}
        description: ${{ parameters.description }}
        topics:
          - python
          - fastapi
    
    - id: create-argocd-app
      name: Create ArgoCD Application
      action: argocd:create-application
      input:
        appName: ${{ parameters.name }}-dev
        appNamespace: argocd
        repoUrl: https://github.com/myorg/${{ parameters.name }}
        path: deploy/overlays/dev
    
    - id: register
      name: Register in Backstage
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: '/backstage-catalog.yaml'
```

## CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/build.yml
name: Build and Push

on:
  push:
    branches: [main, develop]
    tags: ['v*']

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Log in to Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=sha,prefix={{branch}}-
      
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: Update manifests
        if: github.ref == 'refs/heads/main'
        run: |
          # Clone GitOps repo
          git clone https://x-token:${{ secrets.GITOPS_TOKEN }}@github.com/myorg/gitops-manifests
          cd gitops-manifests
          
          # Update image tag
          cd apps/dev/python-app
          kustomize edit set image python-app=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          
          # Commit and push
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add .
          git commit -m "Update python-app to ${{ github.sha }}"
          git push
```

### GitLab CI Pipeline

```yaml
# .gitlab-ci.yml
stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  DOCKER_IMAGE_LATEST: $CI_REGISTRY_IMAGE:latest

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $DOCKER_IMAGE -t $DOCKER_IMAGE_LATEST .
    - docker push $DOCKER_IMAGE
    - docker push $DOCKER_IMAGE_LATEST

update-manifests:
  stage: deploy
  image: alpine/git:latest
  only:
    - main
  script:
    - apk add --no-cache curl
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | sh
    - git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com/myorg/gitops-manifests.git
    - cd gitops-manifests/apps/dev/python-app
    - ../../../kustomize edit set image python-app=$DOCKER_IMAGE
    - git config user.email "<EMAIL>"
    - git config user.name "GitLab CI"
    - git add .
    - git commit -m "Update python-app to $CI_COMMIT_SHA"
    - git push
```

## Environment Promotion

### Manual Promotion

```bash
# Promote from dev to staging
cd gitops-manifests/apps/staging/python-app

# Update kustomization to use dev's current image
kustomize edit set image python-app=ghcr.io/myorg/python-app:abc123

# Commit and push
git add kustomization.yaml
git commit -m "Promote python-app to staging: abc123"
git push
```

### Automated Promotion

```yaml
# .github/workflows/promote.yml
name: Promote to Production

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to promote'
        required: true

jobs:
  promote:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout GitOps repo
        uses: actions/checkout@v3
        with:
          repository: myorg/gitops-manifests
          token: ${{ secrets.GITOPS_TOKEN }}
      
      - name: Update production manifest
        run: |
          cd apps/prod/python-app
          kustomize edit set image python-app=ghcr.io/myorg/python-app:${{ github.event.inputs.version }}
          
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          commit-message: "Promote python-app to production: ${{ github.event.inputs.version }}"
          title: "Promote python-app to production"
          body: |
            Promoting python-app version ${{ github.event.inputs.version }} to production.
            
            Please review and merge to deploy.
          branch: promote-python-app-${{ github.event.inputs.version }}
```

## Secrets Management

### Sealed Secrets

```yaml
# Install sealed-secrets controller
kubectl apply -f https://github.com/bitnami-labs/sealed-secrets/releases/download/v0.24.0/controller.yaml

# Create secret
echo -n '********************************/db' | kubectl create secret generic app-secrets --dry-run=client --from-file=database-url=/dev/stdin -o yaml | kubeseal -o yaml > sealed-secret.yaml
```

```yaml
# sealed-secret.yaml
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  name: app-secrets
  namespace: dev
spec:
  encryptedData:
    database-url: AgA3d4B...encrypted...data
```

### External Secrets with Vault

```yaml
# external-secret.yaml
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secrets
spec:
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  refreshInterval: 15s
  target:
    name: app-secrets
    template:
      type: Opaque
      data:
        database-url: "postgresql://{{ .username }}:{{ .password }}@{{ .host }}:5432/{{ .database }}"
  dataFrom:
    - extract:
        key: /secret/data/apps/python-app
```

## Monitoring GitOps

### ArgoCD Metrics

```yaml
# ServiceMonitor for ArgoCD
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: argocd-metrics
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: argocd-metrics
  endpoints:
  - port: metrics
```

### Deployment Notifications

```yaml
# ArgoCD notification
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
data:
  service.slack: |
    token: $slack-token
  template.app-deployed: |
    message: |
      {{if eq .serviceType "slack"}}:white_check_mark:{{end}} Application {{.app.metadata.name}} is now running new version.
  trigger.on-deployed: |
    - when: app.status.operationState.phase in ['Succeeded']
      send: [app-deployed]
```

## Rollback Strategies

### ArgoCD Rollback

```bash
# List application history
argocd app history python-app-prod

# Rollback to previous sync
argocd app rollback python-app-prod 123

# Rollback to specific revision
argocd app rollback python-app-prod --revision abc123def
```

### Git Revert

```bash
# Revert the commit that updated manifests
git revert <commit-hash>
git push

# ArgoCD will automatically sync the revert
```

## Best Practices

### 1. Separate Code and Config Repos

```
# Application repository
myorg/python-app/        # Application code
  ├── app/
  ├── Dockerfile
  └── .github/workflows/

# Configuration repository  
myorg/gitops-manifests/  # Kubernetes manifests
  ├── apps/
  └── argocd/
```

### 2. Use Semantic Versioning

```bash
# Tag releases properly
git tag -a v1.2.3 -m "Release version 1.2.3"
git push origin v1.2.3

# Reference in production
kustomize edit set image app=myapp:v1.2.3
```

### 3. Environment-Specific Branches

```
main     → production
staging  → staging  
develop  → development
```

### 4. Progressive Delivery

```yaml
# Canary deployment with Flagger
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: python-app
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: python-app
  progressDeadlineSeconds: 60
  service:
    port: 80
  analysis:
    interval: 30s
    threshold: 5
    maxWeight: 50
    stepWeight: 10
    metrics:
    - name: request-success-rate
      thresholdRange:
        min: 99
      interval: 1m
```

---

[← Kubernetes Deployment](kubernetes.md) | [Documentation Home](README.md) | [Task Commands →](tasks.md)