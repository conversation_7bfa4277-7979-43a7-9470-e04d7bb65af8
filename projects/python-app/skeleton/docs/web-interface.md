# Web Interface Guide

This guide covers the web interface implementation, templating system, and UI development in the Python Application Template.

## Overview

The template includes a server-side rendered web interface using:
- **Jinja2**: Template engine for HTML generation
- **Bootstrap 5**: Responsive UI framework
- **FastAPI**: Serving templates and static files
- **HTMX-ready**: For progressive enhancement

## Template Structure

```
app/
├── templates/
│   ├── base.html      # Base template with common layout
│   ├── index.html     # Home page
│   ├── todos.html     # Todo management page
│   └── kafka.html     # Kafka testing page
└── static/
    ├── css/
    │   └── custom.css # Custom styles
    ├── js/
    │   └── app.js     # Application JavaScript
    └── images/        # Static images
```

## Base Template

The `base.html` template provides the common layout:

The base template provides:
- HTML document structure with Bootstrap CSS
- Navigation bar with links to main sections
- Content blocks for page-specific content
- Script includes for Bootstrap JavaScript

## Creating Pages

### Basic Page Template

Page templates extend the base template and define:
- Custom page title
- Page-specific content in the content block
- Additional CSS or JavaScript if needed

### Adding Routes

```python
# app/main.py
from fastapi import Request
from fastapi.templating import Jinja2Templates

templates = Jinja2Templates(directory="app/templates")

@app.get("/new-page")
async def new_page(request: Request):
    return templates.TemplateResponse(
        "new_page.html",
        {
            "request": request,
            "title": "New Page",
            "data": await get_page_data()
        }
    )
```

## Template Features

### Dynamic Content

Templates support dynamic content including:
- Variable display with automatic escaping
- Conditional blocks for showing/hiding content
- Loops for rendering lists and collections
- Filters for formatting data (dates, text truncation, etc.)

### Forms

```html
<!-- Todo form example -->
<form method="POST" action="/todos/add">
    <div class="mb-3">
        <label for="title" class="form-label">Title</label>
        <input type="text" 
               class="form-control" 
               id="title" 
               name="title" 
               required>
    </div>
    
    <div class="mb-3">
        <label for="description" class="form-label">Description</label>
        <textarea class="form-control" 
                  id="description" 
                  name="description" 
                  rows="3"></textarea>
    </div>
    
    <button type="submit" class="btn btn-primary">Add Todo</button>
</form>
```

### Flash Messages

```python
# app/main.py
from fastapi import Request, Form
from fastapi.responses import RedirectResponse

@app.post("/todos/add")
async def add_todo(
    request: Request,
    title: str = Form(...),
    description: str = Form(None)
):
    # Add todo to database
    await create_todo(title, description)
    
    # Set flash message (requires session middleware)
    request.session["flash"] = "Todo added successfully!"
    
    return RedirectResponse(url="/todos", status_code=303)
```

Flash messages are displayed using Bootstrap alerts that:
- Show success/error messages after form submissions
- Can be dismissed by users
- Automatically fade out after a timeout

## Static Files

### Serving Static Files

```python
# app/main.py
from fastapi.staticfiles import StaticFiles

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")
```

### Custom CSS

```css
/* app/static/css/custom.css */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.todo-item {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.todo-item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}
```

### JavaScript Enhancement

```javascript
// app/static/js/app.js
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }, 5000);
    });
    
    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item?')) {
                e.preventDefault();
            }
        });
    });
});
```

## Component Examples

### Card Component

Reusable card components provide:
- Consistent styling with Bootstrap cards
- Optional header, body, and footer sections
- Flexible content rendering
- Easy integration across multiple pages

### Table Component

Responsive data tables include:
- Bootstrap table styling with hover effects
- Dynamic content rendering from database
- Status badges for visual indicators
- Action buttons for edit/delete operations
- Mobile-responsive design

### Modal Dialog

```html
<!-- Modal template -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/items/add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Trigger button -->
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
    Add New Item
</button>
```

## HTMX Integration

### Basic HTMX Setup

HTMX integration enables:
- Dynamic content updates without page reloads
- Seamless form submissions with partial page updates
- Real-time status toggles and data refresh
- Enhanced user experience with minimal JavaScript

### HTMX Endpoints

```python
@app.post("/api/todos/{todo_id}/toggle")
async def toggle_todo(todo_id: int, request: Request):
    todo = await toggle_todo_status(todo_id)
    
    # Return partial HTML
    return templates.TemplateResponse(
        "partials/todo_item.html",
        {"request": request, "todo": todo}
    )
```

## Form Validation

### Client-Side Validation

```html
<form id="todoForm" method="POST" action="/todos/add">
    <div class="mb-3">
        <input type="text" 
               class="form-control" 
               name="title" 
               required 
               minlength="3"
               maxlength="100"
               pattern="[A-Za-z0-9\s]+"
               title="Only letters, numbers, and spaces allowed">
        <div class="invalid-feedback">
            Please provide a valid title.
        </div>
    </div>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>

<script>
// Bootstrap validation
(function() {
    'use strict';
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
})();
</script>
```

### Server-Side Validation

```python
from pydantic import BaseModel, validator

class TodoForm(BaseModel):
    title: str
    description: str = None
    
    @validator('title')
    def title_valid(cls, v):
        if len(v) < 3:
            raise ValueError('Title must be at least 3 characters')
        return v

@app.post("/todos/add")
async def add_todo(request: Request, form_data: TodoForm = Form()):
    try:
        # Validation happens automatically
        todo = await create_todo(form_data)
        return RedirectResponse(url="/todos", status_code=303)
    except ValueError as e:
        return templates.TemplateResponse(
            "todos.html",
            {"request": request, "error": str(e)}
        )
```

## Responsive Design

### Mobile-First Layout

```html
<!-- Responsive grid -->
<div class="container">
    <div class="row">
        <div class="col-12 col-md-8 col-lg-9">
            <!-- Main content -->
        </div>
        <div class="col-12 col-md-4 col-lg-3">
            <!-- Sidebar -->
        </div>
    </div>
</div>

<!-- Responsive utilities -->
<div class="d-none d-md-block">
    <!-- Hidden on mobile -->
</div>

<div class="d-md-none">
    <!-- Visible only on mobile -->
</div>
```

### Navigation Toggle

```html
<!-- Mobile-friendly navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
        <a class="navbar-brand" href="/">App Name</a>
        <button class="navbar-toggler" 
                type="button" 
                data-bs-toggle="collapse" 
                data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/">Home</a>
                </li>
                <!-- More items -->
            </ul>
        </div>
    </div>
</nav>
```

## Theming

### Dark Mode Support

```css
/* app/static/css/theme.css */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2a2a2a;
    --border-color: #3a3a3a;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}
```

```javascript
// Theme toggle
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
}

// Load saved theme
const savedTheme = localStorage.getItem('theme') || 'light';
document.documentElement.setAttribute('data-theme', savedTheme);
```

## Performance Optimization

### Template Caching

```python
# Enable template caching in production
if not settings.debug:
    templates = Jinja2Templates(
        directory="app/templates",
        auto_reload=False  # Disable auto-reload
    )
```

### Asset Optimization

```html
<!-- Preload critical assets -->
<link rel="preload" href="/static/css/custom.css" as="style">
<link rel="preload" href="/static/js/app.js" as="script">

<!-- Lazy load images -->
<img src="placeholder.jpg" 
     data-src="actual-image.jpg" 
     class="lazy-load" 
     loading="lazy">
```

### Minification

```python
# Minify HTML output
from htmlmin import minify

@app.middleware("http")
async def minify_html(request: Request, call_next):
    response = await call_next(request)
    if response.headers.get("content-type", "").startswith("text/html"):
        body = b""
        async for chunk in response.body_iterator:
            body += chunk
        minified = minify(body.decode(), remove_empty_space=True)
        return Response(content=minified, media_type="text/html")
    return response
```

## Security Considerations

### XSS Protection

XSS protection features:
- Automatic HTML escaping for all user input
- Safe rendering of variables by default
- Manual override available for trusted content
- Protection against script injection attacks

### CSRF Protection

```python
# Add CSRF token to forms
from fastapi_csrf_protect import CsrfProtect

@app.post("/todos/add")
async def add_todo(request: Request, csrf_protect: CsrfProtect = Depends()):
    await csrf_protect.validate_csrf(request)
    # Process form
```

CSRF protection includes:
- Hidden tokens in all forms
- Automatic validation on form submission
- Protection against cross-site request forgery
- Integration with FastAPI security middleware

---

[← Kafka Integration](kafka.md) | [Documentation Home](README.md) | [Docker Guide →](docker.md)