# Docker Guide

This guide covers Docker containerization, building images, and running the Python Application Template in containers.

## Docker Overview

The template includes:
- **Multi-stage Dockerfile**: Optimized for size and security
- **Docker Compose**: Local development environment
- **Multi-architecture**: Support for amd64 and arm64
- **Health checks**: Container health monitoring

## Dockerfile

### Production Dockerfile

```dockerfile
# Build stage
FROM python:3.12-slim as builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Runtime stage
FROM python:3.12-slim

WORKDIR /app

# Create non-root user
RUN useradd -m -u 1000 appuser

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser

# Add local bin to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')"

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Development Dockerfile

```dockerfile
# Development Dockerfile with hot-reload
FROM python:3.12-slim

WORKDIR /app

# Install development tools
RUN apt-get update && apt-get install -y \
    gcc \
    git \
    vim \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt requirements-dev.txt ./
RUN pip install --no-cache-dir -r requirements.txt -r requirements-dev.txt

# Copy application code
COPY . .

# Development port
EXPOSE 8000

# Run with reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## Building Images

### Basic Build

```bash
# Build the image
docker build -t python-app:latest .

# Build with specific tag
docker build -t python-app:v1.0.0 .

# Build for specific platform
docker build --platform linux/amd64 -t python-app:latest .
```

### Using Task Commands

```bash
# Build image using task
task docker-build

# Build and push to registry
task docker-build-push

# Build with custom tag
DOCKER_TAG=v1.2.3 task docker-build
```

### Multi-Architecture Build

```bash
# Setup buildx
docker buildx create --name mybuilder --use

# Build for multiple platforms
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t myregistry/python-app:latest \
  --push .
```

## Docker Compose

### Development Environment

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/app_db
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - DEBUG=true
    volumes:
      - ./app:/app/app  # Mount source code for hot-reload
    depends_on:
      - postgres
      - kafka
    networks:
      - app-network

  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: app_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - app-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
```

### Running with Docker Compose

```bash
# Start all services
docker compose up

# Start in background
docker compose up -d

# Start specific services
docker compose up app postgres

# View logs
docker compose logs -f app

# Stop services
docker compose down

# Stop and remove volumes
docker compose down -v
```

## Container Configuration

### Environment Variables

```bash
# Run with environment variables
docker run -e DATABASE_URL=postgresql://... \
           -e DEBUG=false \
           -p 8000:8000 \
           python-app:latest

# Using env file
docker run --env-file .env -p 8000:8000 python-app:latest
```

### Volumes and Mounts

```bash
# Mount configuration
docker run -v $(pwd)/config:/app/config:ro python-app:latest

# Mount for persistent data
docker run -v mydata:/app/data python-app:latest

# Development with source code mount
docker run -v $(pwd)/app:/app/app python-app:latest
```

### Networking

```bash
# Create custom network
docker network create app-network

# Run container in network
docker run --network app-network --name app python-app:latest

# Connect to running container
docker network connect app-network existing-container
```

## Health Checks

### Dockerfile Health Check

```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1
```

### Docker Compose Health Check

```yaml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### Application Health Endpoint

```python
@app.get("/health")
async def health_check():
    """Basic health check."""
    return {"status": "healthy"}

@app.get("/health/ready")
async def readiness_check():
    """Detailed readiness check."""
    checks = {}
    
    # Check database
    try:
        await check_database()
        checks["database"] = "ok"
    except Exception as e:
        checks["database"] = f"error: {str(e)}"
    
    # Check Kafka
    try:
        await check_kafka()
        checks["kafka"] = "ok"
    except Exception as e:
        checks["kafka"] = f"error: {str(e)}"
    
    status = "ready" if all(v == "ok" for v in checks.values()) else "not ready"
    return {"status": status, "checks": checks}
```

## Security Best Practices

### Non-Root User

```dockerfile
# Create non-root user
RUN useradd -m -u 1000 appuser

# Change ownership
COPY --chown=appuser:appuser . .

# Switch to non-root user
USER appuser
```

### Minimal Base Image

```dockerfile
# Use distroless for minimal attack surface
FROM gcr.io/distroless/python3-debian11

# Or Alpine for small size
FROM python:3.12-alpine
```

### Secret Management

```yaml
# docker-compose with secrets
services:
  app:
    secrets:
      - db_password
      - api_key
    environment:
      DATABASE_PASSWORD_FILE: /run/secrets/db_password

secrets:
  db_password:
    file: ./secrets/db_password.txt
  api_key:
    file: ./secrets/api_key.txt
```

### Security Scanning

```bash
# Scan image for vulnerabilities
docker scan python-app:latest

# Using Trivy
trivy image python-app:latest

# Using Snyk
snyk container test python-app:latest
```

## Optimization Techniques

### Multi-Stage Build Optimization

```dockerfile
# Optimize for cache
FROM python:3.12-slim as builder

# Copy only requirements first
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Copy source code later
COPY . .

# This way, code changes don't invalidate pip cache
```

### Layer Caching

```dockerfile
# Order matters for caching
# Less frequently changed files first
COPY requirements.txt .
RUN pip install -r requirements.txt

# More frequently changed files last
COPY app/ app/
```

### Size Optimization

```dockerfile
# Combine RUN commands
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        libc6-dev \
    && pip install --no-cache-dir -r requirements.txt \
    && apt-get purge -y --auto-remove gcc libc6-dev \
    && rm -rf /var/lib/apt/lists/*
```

## Development Workflow

### Hot Reload Setup

```yaml
# docker-compose.override.yml for development
services:
  app:
    volumes:
      - ./app:/app/app:cached
    environment:
      - RELOAD=true
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--reload"]
```

### Debugging in Container

```bash
# Run with interactive shell
docker run -it python-app:latest /bin/bash

# Execute command in running container
docker exec -it container-name python

# Attach to running container
docker attach container-name

# View real-time logs
docker logs -f container-name
```

### Development Tools

```dockerfile
# Development image with tools
FROM python:3.12-slim-dev

RUN apt-get update && apt-get install -y \
    ipython \
    httpie \
    postgresql-client \
    redis-tools \
    && pip install \
    ipdb \
    pytest \
    black \
    flake8
```

## Deployment Patterns

### Using Registry

```bash
# Tag for registry
docker tag python-app:latest myregistry.com/python-app:v1.0.0

# Push to registry
docker push myregistry.com/python-app:v1.0.0

# Pull from registry
docker pull myregistry.com/python-app:v1.0.0
```

### Container Orchestration

```yaml
# docker-compose for production
services:
  app:
    image: myregistry.com/python-app:v1.0.0
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
```

## Monitoring

### Container Metrics

```bash
# View resource usage
docker stats

# View specific container
docker stats container-name

# Format output
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

### Logging

```yaml
# Configure logging driver
services:
  app:
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"
```

### Health Monitoring

```bash
# Check container health
docker inspect --format='{{.State.Health.Status}}' container-name

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' container-name
```

## Troubleshooting

### Common Issues

1. **Container exits immediately**
```bash
# Check logs
docker logs container-name

# Run with shell to debug
docker run -it python-app:latest /bin/bash
```

2. **Permission errors**
```bash
# Fix ownership
docker run --user $(id -u):$(id -g) python-app:latest
```

3. **Network connectivity**
```bash
# Test from inside container
docker run python-app:latest curl http://postgres:5432
```

### Debug Commands

```bash
# Inspect image layers
docker history python-app:latest

# View image details
docker inspect python-app:latest

# Export container filesystem
docker export container-name > container.tar

# View running processes
docker top container-name
```

---

[← Web Interface](web-interface.md) | [Documentation Home](README.md) | [Kubernetes Deployment →](kubernetes.md)