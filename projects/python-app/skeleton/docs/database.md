# Database Integration Guide

This guide covers database setup, usage, and best practices for the Python Application Template.

## Overview

The template uses:
- **SQLAlchemy 2.0**: Modern async ORM
- **PostgreSQL**: Primary database (production)
- **SQLite**: Alternative for testing/development
- **Alembic**: Database migrations (optional)

## Database Setup

### PostgreSQL with Docker

The easiest way to run PostgreSQL locally:

```bash
# Start PostgreSQL
docker compose up -d postgres

# Verify it's running
docker compose ps

# Connect to database
docker compose exec postgres psql -U postgres -d app_db
```

### Connection String Format

```bash
# PostgreSQL
postgresql+asyncpg://username:password@host:port/database

# PostgreSQL with SSL
postgresql+asyncpg://username:password@host:port/database?ssl=require

# SQLite (for testing)
sqlite+aiosqlite:///./database.db
```

### Environment Configuration

```env
# .env file
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/app_db

# Additional settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=0
DB_POOL_TIMEOUT=30
DB_ECHO=false  # Set to true to log SQL queries
```

## Database Models

### Current Schema

The template includes a `Todo` model as an example:

```python
# app/database.py
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func

class Todo(Base):
    __tablename__ = "todos"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    description = Column(String, nullable=True)
    completed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
```

### Adding New Models

Create new models by extending the `Base` class:

```python
from sqlalchemy import Column, Integer, String, ForeignKey, Text
from sqlalchemy.orm import relationship

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, nullable=False, index=True)
    username = Column(String, unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    posts = relationship("Post", back_populates="author")

class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    content = Column(Text)
    author_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    author = relationship("User", back_populates="posts")
```

### Model Best Practices

1. **Always use indexes** on frequently queried columns:
```python
email = Column(String, unique=True, index=True)
```

2. **Add timestamps** to all models:
```python
created_at = Column(DateTime(timezone=True), server_default=func.now())
updated_at = Column(DateTime(timezone=True), onupdate=func.now())
```

3. **Use appropriate column types**:
```python
# For UUIDs
id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

# For JSON data
metadata = Column(JSON, default=dict)

# For enums
from enum import Enum
class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"

status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE)
```

## Database Operations

### Basic CRUD Operations

#### Create

```python
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import Todo

async def create_todo(db: AsyncSession, title: str, description: str = None):
    todo = Todo(title=title, description=description)
    db.add(todo)
    await db.commit()
    await db.refresh(todo)
    return todo
```

#### Read

```python
from sqlalchemy import select

# Get all todos
async def get_todos(db: AsyncSession):
    result = await db.execute(select(Todo))
    return result.scalars().all()

# Get single todo
async def get_todo(db: AsyncSession, todo_id: int):
    result = await db.execute(
        select(Todo).where(Todo.id == todo_id)
    )
    return result.scalar_one_or_none()

# With filtering
async def get_active_todos(db: AsyncSession):
    result = await db.execute(
        select(Todo).where(Todo.completed == False)
    )
    return result.scalars().all()
```

#### Update

```python
async def update_todo(db: AsyncSession, todo_id: int, **kwargs):
    todo = await get_todo(db, todo_id)
    if not todo:
        return None
    
    for key, value in kwargs.items():
        setattr(todo, key, value)
    
    await db.commit()
    await db.refresh(todo)
    return todo
```

#### Delete

```python
async def delete_todo(db: AsyncSession, todo_id: int):
    todo = await get_todo(db, todo_id)
    if not todo:
        return False
    
    await db.delete(todo)
    await db.commit()
    return True
```

### Advanced Queries

#### Pagination

```python
async def get_todos_paginated(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100
):
    result = await db.execute(
        select(Todo)
        .offset(skip)
        .limit(limit)
        .order_by(Todo.created_at.desc())
    )
    return result.scalars().all()
```

#### Joins

```python
# Eager loading with selectinload
from sqlalchemy.orm import selectinload

async def get_users_with_posts(db: AsyncSession):
    result = await db.execute(
        select(User).options(selectinload(User.posts))
    )
    return result.scalars().all()

# Join query
async def get_posts_with_authors(db: AsyncSession):
    result = await db.execute(
        select(Post, User)
        .join(User, Post.author_id == User.id)
        .where(User.is_active == True)
    )
    return result.all()
```

#### Aggregations

```python
from sqlalchemy import func

# Count
async def count_todos(db: AsyncSession):
    result = await db.execute(
        select(func.count(Todo.id))
    )
    return result.scalar()

# Group by
async def todos_by_status(db: AsyncSession):
    result = await db.execute(
        select(Todo.completed, func.count(Todo.id))
        .group_by(Todo.completed)
    )
    return result.all()
```

### Transactions

```python
async def transfer_todos(
    db: AsyncSession, 
    from_user_id: int, 
    to_user_id: int
):
    async with db.begin():
        # All operations in this block are in a transaction
        todos = await db.execute(
            select(Todo).where(Todo.user_id == from_user_id)
        )
        for todo in todos.scalars():
            todo.user_id = to_user_id
        
        # Automatically commits if no exception
        # Automatically rolls back if exception occurs
```

## Database Connection Management

### Connection Pool Configuration

```python
# app/database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Create engine with connection pool
engine = create_async_engine(
    settings.database_url,
    pool_size=settings.db_pool_size,
    max_overflow=settings.db_max_overflow,
    pool_timeout=settings.db_pool_timeout,
    pool_pre_ping=True,  # Verify connections before use
    echo=settings.debug,  # Log SQL in debug mode
)

# Session factory
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)
```

### Dependency Injection

```python
# app/database.py
async def get_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Usage in routes
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

@app.get("/todos")
async def read_todos(db: AsyncSession = Depends(get_db)):
    todos = await get_todos(db)
    return todos
```

## Migrations

### Setting Up Alembic

```bash
# Install Alembic
pip install alembic

# Initialize Alembic
alembic init alembic

# Configure alembic.ini
sqlalchemy.url = postgresql+asyncpg://user:pass@localhost/dbname
```

### Creating Migrations

```bash
# Auto-generate migration
alembic revision --autogenerate -m "Add user table"

# Create empty migration
alembic revision -m "Custom migration"
```

### Migration Example

```python
# alembic/versions/xxx_add_user_table.py
from alembic import op
import sqlalchemy as sa

def upgrade():
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
    )
    op.create_index('ix_users_email', 'users', ['email'])

def downgrade():
    op.drop_index('ix_users_email')
    op.drop_table('users')
```

### Running Migrations

```bash
# Apply all migrations
alembic upgrade head

# Rollback one migration
alembic downgrade -1

# View migration history
alembic history

# Show current revision
alembic current
```

## Testing with Databases

### Test Database Setup

```python
# tests/conftest.py
import pytest
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from app.database import Base

@pytest.fixture
async def test_db():
    # Use SQLite for tests
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False}
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with AsyncSession(engine) as session:
        yield session
    
    # Cleanup
    await engine.dispose()
```

### Database Test Example

```python
# tests/test_todos.py
import pytest
from app.database import Todo

@pytest.mark.asyncio
async def test_create_todo(test_db):
    # Create todo
    todo = Todo(title="Test", description="Test todo")
    test_db.add(todo)
    await test_db.commit()
    
    # Verify
    assert todo.id is not None
    assert todo.title == "Test"
```

## Performance Optimization

### Query Optimization

1. **Use indexes effectively**:
```python
# Add composite index
Index('idx_user_status', User.email, User.is_active)
```

2. **Avoid N+1 queries**:
```python
# Bad: N+1 query
users = await get_users(db)
for user in users:
    posts = await get_user_posts(db, user.id)  # N queries

# Good: Eager loading
users = await db.execute(
    select(User).options(selectinload(User.posts))
)
```

3. **Use bulk operations**:
```python
# Bulk insert
await db.execute(
    insert(Todo),
    [
        {"title": "Todo 1", "description": "Desc 1"},
        {"title": "Todo 2", "description": "Desc 2"},
    ]
)
await db.commit()
```

### Connection Pool Tuning

```python
# Production settings
engine = create_async_engine(
    DATABASE_URL,
    pool_size=50,          # Number of connections
    max_overflow=10,       # Extra connections when needed
    pool_timeout=30,       # Wait time for connection
    pool_recycle=3600,     # Recycle connections after 1 hour
    pool_pre_ping=True,    # Test connections before use
)
```

## Monitoring and Debugging

### Enable SQL Logging

```python
# Development only
engine = create_async_engine(
    DATABASE_URL,
    echo=True,  # Log all SQL
    echo_pool=True  # Log connection pool events
)
```

### Query Performance Analysis

```python
import time
from contextlib import asynccontextmanager

@asynccontextmanager
async def timed_query(name: str):
    start = time.time()
    yield
    duration = time.time() - start
    if duration > 1.0:  # Log slow queries
        logger.warning(f"Slow query '{name}': {duration:.2f}s")
```

### Database Health Checks

```python
async def check_database_health(db: AsyncSession):
    try:
        # Simple query to test connection
        await db.execute(select(1))
        return {"database": "healthy"}
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {"database": "unhealthy", "error": str(e)}
```

## Common Issues and Solutions

### Connection Pool Exhaustion

**Problem**: "TimeoutError: QueuePool limit reached"

**Solution**:
```python
# Increase pool size
pool_size=50
max_overflow=10

# Ensure sessions are closed
async with get_db() as db:
    # Session automatically closed
    pass
```

### Async Context Errors

**Problem**: "RuntimeError: Task attached to a different loop"

**Solution**:
```python
# Use the same event loop
async def main():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
```

### Migration Conflicts

**Problem**: "Multiple head revisions"

**Solution**:
```bash
# Merge migrations
alembic merge -m "Merge heads"

# Or specify target
alembic upgrade heads
```

---

[← API Reference](api-reference.md) | [Documentation Home](README.md) | [Kafka Integration →](kafka.md)