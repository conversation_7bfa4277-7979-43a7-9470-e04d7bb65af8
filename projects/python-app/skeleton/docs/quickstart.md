# Quick Start Guide

Get the Python Application Template running in 5 minutes! This guide assumes you've completed the [Installation](installation.md).

## 🚀 Quick Start Commands

```bash
# 1. Start all services with Docker Compose
docker compose up -d

# 2. Run the application
task run

# 3. Open your browser
open http://localhost:8000
```

That's it! The application is now running with a PostgreSQL database and Kafka.

## 📋 Step-by-Step Guide

### Step 1: Start Dependencies

Start PostgreSQL and Kafka using Docker Compose:

```bash
# Start all services in the background
docker compose up -d

# Verify services are running
docker compose ps
```

You should see:
- `postgres` - Running on port 5432
- `kafka` - Running on port 9092
- `zookeeper` - Running on port 2181

### Step 2: Run the Application

```bash
# Using task (recommended)
task run

# Or directly with Python
python run.py
```

You'll see output like:
```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
```

### Step 3: Explore the Application

Open your browser and navigate to:
- **Home Page**: http://localhost:8000
- **Kafka Demo**: http://localhost:8000/kafka
- **Todo List**: http://localhost:8000/todos

### Step 4: Test Core Features

#### Test Database (Todo List)
1. Go to http://localhost:8000/todos
2. Add a new todo item
3. Mark items as complete
4. Delete items

#### Test Kafka Integration
1. Go to http://localhost:8000/kafka
2. Send a test message
3. Watch it appear in the message list
4. Try deleting individual messages

## 🎯 Common Development Workflows

### Running Without Kafka

If you don't need Kafka functionality:

```bash
# Start only PostgreSQL
docker compose up -d postgres

# Run the app
task run
```

### Using SQLite Instead of PostgreSQL

For quick testing without any external dependencies:

```bash
# Set environment variable
export DATABASE_URL=sqlite+aiosqlite:///./test_app.db

# Run the app
task run
```

### Running in Debug Mode

```bash
# Enable debug mode for detailed logs
export DEBUG=true
task run
```

### Running Tests

```bash
# Run all tests
task test

# Run with coverage
task test-coverage
```

## 🛠️ Available Task Commands

Here are the most useful commands for development:

| Command | Description |
|---------|-------------|
| `task run` | Start the application with hot-reload |
| `task install` | Install Python dependencies |
| `task test` | Run all tests |
| `task docker-build` | Build Docker image |
| `task --list` | Show all available tasks |

## 🔧 Quick Configuration

### Basic Configuration

Create a `.env` file with minimal settings:

```env
APP_NAME=my-python-app
DEBUG=true
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/app_db
```

### Kafka Configuration (Optional)

Add Kafka settings to `.env`:

```env
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_NAME=my-app-events
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
```

## 📊 Monitoring Your Application

### Check Application Health

```bash
# Check if app is responding
curl http://localhost:8000/health

# Check detailed health
curl http://localhost:8000/health/ready
```

### View Logs

```bash
# Application logs (if running in terminal, they're already visible)

# PostgreSQL logs
docker compose logs postgres

# Kafka logs
docker compose logs kafka
```

### Database Access

```bash
# Connect to PostgreSQL
docker compose exec postgres psql -U postgres -d app_db

# List tables
\dt

# View todos
SELECT * FROM todos;
```

## 🎨 Customization Quick Tips

### Change the Port

```bash
# Use environment variable
export APP_PORT=3000
task run

# Or in .env file
APP_PORT=3000
```

### Add a New Page

1. Create a new template in `app/templates/`
2. Add a route in `app/main.py`
3. Restart the application (auto-reload will handle this)

### Modify the Database Schema

1. Edit models in `app/database.py`
2. The schema updates automatically on startup
3. For production, use proper migrations

## 🚨 Troubleshooting Quick Fixes

### Port Already in Use

```bash
# Find what's using port 8000
lsof -i :8000

# Kill the process or use a different port
export APP_PORT=8001
task run
```

### Database Connection Failed

```bash
# Ensure PostgreSQL is running
docker compose ps

# Restart PostgreSQL
docker compose restart postgres

# Check logs
docker compose logs postgres
```

### Kafka Connection Issues

```bash
# Restart Kafka stack
docker compose restart kafka zookeeper

# Test Kafka connection
task kafka-test-local
```

## 🎉 What's Next?

Congratulations! You now have a running Python application. Here's what to explore next:

1. **Understand the Code**: Read the [Architecture Overview](architecture.md)
2. **Configure Your App**: Check the [Configuration Guide](configuration.md)
3. **Start Developing**: Follow the [Development Guide](development.md)
4. **Deploy to Production**: See [Kubernetes Deployment](kubernetes.md)

### Quick Experiments to Try

1. **Modify the Home Page**: Edit `app/templates/index.html` and see changes instantly
2. **Add an API Endpoint**: Add a new route in `app/main.py`
3. **Send Kafka Messages**: Use the Kafka page to send different message types
4. **Create Custom Tasks**: Add new commands to `Taskfile.yml`

---

[← Installation](installation.md) | [Documentation Home](README.md) | [Development Guide →](development.md)