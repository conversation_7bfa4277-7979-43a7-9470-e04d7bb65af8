# Kubernetes Deployment Guide

This guide covers deploying the Python Application Template to Kubernetes, including manifests, configurations, and best practices.

## Overview

The template includes:
- **Kustomize-based deployment**: Base and overlay configurations
- **Resource definitions**: Deployment, Service, Ingress
- **Health checks**: Liveness and readiness probes
- **Auto-scaling**: HPA configuration
- **GitOps ready**: ArgoCD compatible

## Project Structure  

```
deploy/
├── base/                    # Base Kubernetes resources
│   ├── deployment.yaml     # Application deployment
│   ├── service.yaml        # Service definition
│   ├── ingress.yaml        # Ingress rules
│   └── kustomization.yaml  # Kustomize configuration
└── overlays/               # Environment-specific configs
    ├── dev/
    │   ├── deployment-patch.yaml
    │   ├── ingress-patch.yaml
    │   └── kustomization.yaml
    └── prod/
        ├── deployment-patch.yaml
        ├── ingress-patch.yaml
        └── kustomization.yaml
```

## Base Configuration

### Deployment

```yaml
# deploy/base/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: python-app
  labels:
    app: python-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: python-app
  template:
    metadata:
      labels:
        app: python-app
    spec:
      containers:
      - name: app
        image: python-app:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: APP_NAME
          value: "Python App"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Service

```yaml
# deploy/base/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: python-app
  labels:
    app: python-app
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: python-app
```

### Ingress

```yaml
# deploy/base/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: python-app
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
  - host: python-app.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: python-app
            port:
              number: 80
```

### Kustomization

```yaml
# deploy/base/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - deployment.yaml
  - service.yaml
  - ingress.yaml

commonLabels:
  app: python-app
  version: v1.0.0
```

## Environment Overlays

### Development Overlay

```yaml
# deploy/overlays/dev/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: dev

bases:
  - ../../base

patchesStrategicMerge:
  - deployment-patch.yaml
  - ingress-patch.yaml

configMapGenerator:
  - name: app-config
    literals:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - ENVIRONMENT=development

secretGenerator:
  - name: app-secrets
    literals:
      - database-url=postgresql+asyncpg://user:pass@postgres-dev:5432/app_dev
```

```yaml
# deploy/overlays/dev/deployment-patch.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: python-app
spec:
  replicas: 1
  template:
    spec:
      containers:
      - name: app
        image: python-app:dev
        env:
        - name: DEBUG
          value: "true"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
```

### Production Overlay

```yaml
# deploy/overlays/prod/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: production

bases:
  - ../../base

patchesStrategicMerge:
  - deployment-patch.yaml
  - ingress-patch.yaml

replicas:
  - name: python-app
    count: 3

images:
  - name: python-app
    newName: myregistry.com/python-app
    newTag: v1.0.0

configMapGenerator:
  - name: app-config
    literals:
      - DEBUG=false
      - LOG_LEVEL=WARNING
      - ENVIRONMENT=production
```

## Deployment Commands

### Using kubectl

```bash
# Deploy to development
kubectl apply -k deploy/overlays/dev

# Deploy to production
kubectl apply -k deploy/overlays/prod

# Dry run
kubectl apply -k deploy/overlays/dev --dry-run=client -o yaml

# Delete deployment
kubectl delete -k deploy/overlays/dev
```

### Using Task Commands

```bash
# Deploy to Kubernetes
task k8s-deploy

# Deploy to specific environment
ENV=prod task k8s-deploy

# Check deployment status
task k8s-status

# View logs
task k8s-logs

# Port forward for testing
task k8s-port-forward
```

## Secrets Management

### Creating Secrets

```bash
# Create secret from literal
kubectl create secret generic app-secrets \
  --from-literal=database-url='postgresql://...' \
  --from-literal=api-key='secret-key'

# Create from file
kubectl create secret generic app-secrets \
  --from-file=database-url=./secrets/db-url.txt

# Create from env file
kubectl create secret generic app-secrets \
  --from-env-file=./secrets/.env
```

### Using Secrets

```yaml
# In deployment
env:
  - name: DATABASE_URL
    valueFrom:
      secretKeyRef:
        name: app-secrets
        key: database-url
        
# Mount as files
volumes:
  - name: secrets
    secret:
      secretName: app-secrets
containers:
  - name: app
    volumeMounts:
      - name: secrets
        mountPath: /etc/secrets
        readOnly: true
```

### External Secrets (Recommended)

```yaml
# Using External Secrets Operator
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secrets
spec:
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: app-secrets
  data:
    - secretKey: database-url
      remoteRef:
        key: secret/data/app
        property: database_url
```

## ConfigMaps

### Application Configuration

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
data:
  config.json: |
    {
      "app_name": "Python App",
      "features": {
        "new_ui": true,
        "beta_api": false
      }
    }
  settings.py: |
    DEBUG = False
    ALLOWED_HOSTS = ['*']
```

### Using ConfigMaps

```yaml
# As environment variables
envFrom:
  - configMapRef:
      name: app-config

# Mount as volume
volumes:
  - name: config
    configMap:
      name: app-config
containers:
  - name: app
    volumeMounts:
      - name: config
        mountPath: /app/config
```

## Auto-scaling

### Horizontal Pod Autoscaler

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: python-app-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: python-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Vertical Pod Autoscaler

```yaml
# vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: python-app-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: python-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: app
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
```

## Monitoring

### Prometheus Metrics

```yaml
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: python-app
spec:
  selector:
    matchLabels:
      app: python-app
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
```

### Application Metrics

```python
# app/metrics.py
from prometheus_client import Counter, Histogram, generate_latest

# Define metrics
request_count = Counter('app_requests_total', 'Total requests')
request_duration = Histogram('app_request_duration_seconds', 'Request duration')

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## Health Checks

### Liveness Probe

```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
```

### Readiness Probe

```yaml
readinessProbe:
  httpGet:
    path: /health/ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  successThreshold: 1
  failureThreshold: 3
```

### Startup Probe

```yaml
startupProbe:
  httpGet:
    path: /health
    port: 8000
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 30
```

## Resource Management

### Resource Requests and Limits

```yaml
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

### Quality of Service Classes

```yaml
# Guaranteed QoS
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "256Mi"  # Same as requests
    cpu: "250m"      # Same as requests

# Burstable QoS
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"  # Higher than requests
    cpu: "500m"      # Higher than requests
```

## Networking

### Network Policies

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: python-app-network-policy
spec:
  podSelector:
    matchLabels:
      app: python-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
```

### Service Mesh (Istio)

```yaml
# virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: python-app
spec:
  hosts:
  - python-app
  http:
  - match:
    - uri:
        prefix: /api/v2
    route:
    - destination:
        host: python-app-v2
        port:
          number: 80
      weight: 20
    - destination:
        host: python-app-v1
        port:
          number: 80
      weight: 80
```

## Troubleshooting

### Common Commands

```bash
# Check pod status
kubectl get pods -l app=python-app

# Describe pod
kubectl describe pod python-app-xxxxx

# View logs
kubectl logs -f deployment/python-app

# Execute command in pod
kubectl exec -it deployment/python-app -- /bin/bash

# Port forward for debugging
kubectl port-forward deployment/python-app 8000:8000

# Check events
kubectl get events --sort-by='.lastTimestamp'
```

### Debug Containers

```yaml
# Enable debug container
spec:
  template:
    spec:
      shareProcessNamespace: true
      containers:
      - name: app
        image: python-app:latest
      - name: debug
        image: busybox
        command: ['sleep', '3600']
        securityContext:
          capabilities:
            add: ["SYS_PTRACE"]
```

### Common Issues

1. **CrashLoopBackOff**
```bash
# Check logs
kubectl logs pod-name --previous

# Check resource limits
kubectl describe pod pod-name | grep -A 5 "Limits"
```

2. **ImagePullBackOff**
```bash
# Check image name
kubectl describe pod pod-name | grep -A 5 "Image"

# Check secrets for private registry
kubectl get secrets
```

3. **Pending Pods**
```bash
# Check node resources
kubectl describe nodes

# Check PVC status
kubectl get pvc
```

## Best Practices

### 1. Use Namespaces

```bash
# Create namespace
kubectl create namespace myapp-prod

# Deploy to namespace
kubectl apply -k deploy/overlays/prod -n myapp-prod
```

### 2. Label Everything

```yaml
metadata:
  labels:
    app: python-app
    version: v1.0.0
    component: backend
    environment: production
    team: platform
```

### 3. Use Resource Quotas

```yaml
apiVersion: v1
kind: ResourceQuota
metadata:
  name: compute-quota
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
```

### 4. Security Context

```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
```

---

[← Docker Guide](docker.md) | [Documentation Home](README.md) | [GitOps Integration →](gitops.md)