# Frequently Asked Questions (FAQ)

This page answers common questions about the Python Application Template.

## General Questions

### What is this template for?

This template provides a production-ready foundation for building Python web applications with:
- FastAPI for high-performance APIs
- PostgreSQL for data persistence  
- Apache Kafka for event streaming
- Kubernetes-ready deployment configurations
- Built-in testing and CI/CD pipelines

It's designed for teams building microservices, APIs, or web applications that need modern cloud-native patterns.

### What Python version is required?

Python 3.12 or higher is required. The template uses modern Python features like:
- Type hints
- Async/await syntax
- Structural pattern matching
- Union types with `|` operator

### Can I use this template for production?

Yes! The template includes production-ready features:
- Security best practices
- Health checks and monitoring
- Proper error handling
- Docker optimization
- Kubernetes manifests
- Resource limits and scaling

However, always review and adjust configurations for your specific requirements.

## Development Questions

### How do I add a new endpoint?

Add new routes to `app/main.py`:

```python
@app.get("/api/new-endpoint")
async def new_endpoint():
    return {"message": "Hello from new endpoint"}
```

For complex APIs, organize routes in separate modules:

```python
# app/routers/users.py
from fastapi import APIRouter

router = APIRouter(prefix="/api/users", tags=["users"])

@router.get("/")
async def get_users():
    return {"users": []}

# In app/main.py
from app.routers import users
app.include_router(users.router)
```

### How do I add a new database table?

1. Define the model in `app/database.py`:
```python
class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False)
    price = Column(Float, nullable=False)
```

2. The table will be created automatically in development. For production, use migrations.

### How do I run without Docker?

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/db
export KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Run the application
python run.py
# Or
uvicorn app.main:app --reload
```

### Can I use MySQL instead of PostgreSQL?

Yes, but you'll need to:

1. Change the database URL:
```env
DATABASE_URL=mysql+aiomysql://user:pass@localhost:3306/db
```

2. Install MySQL driver:
```bash
pip install aiomysql
```

3. Update any PostgreSQL-specific features in your models.

### How do I disable Kafka?

Set environment variable:
```env
KAFKA_ENABLED=false
```

Or modify the startup code:
```python
@app.on_event("startup")
async def startup():
    if settings.kafka_enabled:
        await kafka_manager.start()
```

## Deployment Questions

### How do I deploy to Kubernetes?

```bash
# Build and push image
docker build -t myregistry/python-app:v1.0.0 .
docker push myregistry/python-app:v1.0.0

# Update image in kustomization
cd deploy/overlays/prod
kustomize edit set image python-app=myregistry/python-app:v1.0.0

# Deploy
kubectl apply -k .
```

### What's the recommended way to handle secrets?

For development:
- Use `.env` files (gitignored)
- Use docker-compose secrets

For production:
- Kubernetes Secrets (encrypted at rest)
- External Secrets Operator with Vault/AWS Secrets Manager
- Sealed Secrets for GitOps

Never commit secrets to Git!

### How do I set up auto-scaling?

Use Horizontal Pod Autoscaler:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: python-app-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: python-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### Can I deploy to AWS/GCP/Azure?

Yes! The template works with any cloud provider:

**AWS:**
- EKS for Kubernetes
- RDS for PostgreSQL
- MSK for Kafka
- ECR for container registry

**GCP:**
- GKE for Kubernetes
- Cloud SQL for PostgreSQL
- Cloud Pub/Sub (instead of Kafka)
- Artifact Registry

**Azure:**
- AKS for Kubernetes
- Azure Database for PostgreSQL
- Event Hubs (Kafka-compatible)
- Container Registry

## Testing Questions

### How do I run only unit tests?

```bash
# Mark unit tests
@pytest.mark.unit
def test_unit():
    pass

# Run only unit tests
pytest -m unit

# Skip integration tests
pytest -m "not integration"
```

### Why are my async tests failing?

Common issues:

1. **Missing async marker:**
```python
@pytest.mark.asyncio  # Don't forget this!
async def test_async():
    pass
```

2. **Event loop issues:**
```python
# Add to conftest.py
@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
```

### How do I test with a real database?

```bash
# Start test database
docker run -d -p 5433:5432 -e POSTGRES_PASSWORD=test postgres:15

# Run tests with test database
DATABASE_URL=postgresql+asyncpg://postgres:test@localhost:5433/test pytest
```

## Performance Questions

### How can I improve application performance?

1. **Use connection pooling:**
```python
DB_POOL_SIZE=50
DB_MAX_OVERFLOW=10
```

2. **Enable caching:**
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def expensive_operation(param):
    return result
```

3. **Optimize queries:**
```python
# Use select with specific columns
stmt = select(User.id, User.name).where(User.active == True)

# Use eager loading
stmt = select(User).options(selectinload(User.posts))
```

4. **Use async everywhere:**
```python
# Run concurrent operations
results = await asyncio.gather(
    fetch_from_db(),
    fetch_from_api(),
    fetch_from_cache()
)
```

### What are the resource requirements?

Minimum requirements:
- **Development:** 1 CPU, 512MB RAM
- **Production:** 2 CPUs, 1GB RAM (per instance)

Recommended for production:
- 4-8 instances
- 2-4 CPUs per instance
- 2-4GB RAM per instance
- Adjust based on load

### How do I monitor performance?

1. **Application metrics:**
```python
from prometheus_client import Counter, Histogram

request_count = Counter('requests_total', 'Total requests')
request_duration = Histogram('request_duration_seconds', 'Request duration')
```

2. **Use APM tools:**
- DataDog
- New Relic
- Elastic APM
- OpenTelemetry

3. **Built-in endpoints:**
- `/health` - Basic health
- `/health/ready` - Detailed health
- `/metrics` - Prometheus metrics

## Troubleshooting Questions

### Why can't I connect to the database?

Check these common issues:

1. **Database not running:**
```bash
docker compose ps
docker compose up -d postgres
```

2. **Wrong connection string:**
```bash
echo $DATABASE_URL
# Should be: postgresql+asyncpg://user:pass@host:port/db
```

3. **Network issues:**
```bash
# Test connection
docker compose exec app python -c "import asyncpg; asyncpg.connect('...')"
```

### Why is Kafka not working?

1. **Kafka not started:**
```bash
docker compose up -d kafka zookeeper
# Wait 30 seconds for startup
```

2. **Wrong configuration:**
```bash
echo $KAFKA_BOOTSTRAP_SERVERS
# Should be: localhost:9092 or kafka:9092
```

3. **Topic doesn't exist:**
```bash
# Create topic manually
docker compose exec kafka kafka-topics --create \
  --topic app-events \
  --bootstrap-server localhost:9092
```

### How do I debug production issues?

1. **Check logs:**
```bash
kubectl logs -f deployment/python-app
kubectl logs -f deployment/python-app --previous
```

2. **Enable debug mode temporarily:**
```bash
kubectl set env deployment/python-app DEBUG=true
```

3. **Port forward for testing:**
```bash
kubectl port-forward deployment/python-app 8000:8000
```

4. **Exec into container:**
```bash
kubectl exec -it deployment/python-app -- /bin/bash
```

## Architecture Questions

### Should I use this for microservices?

Yes! The template is designed for microservices:
- Lightweight and focused
- API-first design
- Event-driven with Kafka
- Health checks for orchestration
- Distributed tracing ready

### Can I add GraphQL?

Yes, you can add GraphQL alongside REST:

```bash
pip install strawberry-graphql[fastapi]
```

```python
import strawberry
from strawberry.fastapi import GraphQLRouter

@strawberry.type
class Query:
    @strawberry.field
    def hello(self) -> str:
        return "Hello World"

schema = strawberry.Schema(query=Query)
graphql_app = GraphQLRouter(schema)

app.include_router(graphql_app, prefix="/graphql")
```

### How do I add authentication?

Example with JWT:

```bash
pip install python-jose[cryptography] passlib[bcrypt]
```

```python
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

async def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401)
        return username
    except JWTError:
        raise HTTPException(status_code=401)

@app.get("/protected")
async def protected_route(current_user: str = Depends(get_current_user)):
    return {"user": current_user}
```

### Should I split this into multiple services?

Consider splitting when:
- Different scaling requirements
- Different teams owning different parts
- Different deployment cycles
- Clear bounded contexts

Keep together when:
- Shared data model
- Tight coupling
- Same scaling patterns
- Single team ownership

## Getting More Help

### Where can I find more examples?

1. Check the `examples/` directory in the repository
2. FastAPI documentation: https://fastapi.tiangolo.com
3. SQLAlchemy async docs: https://docs.sqlalchemy.org/en/14/orm/extensions/asyncio.html
4. Kubernetes examples: https://kubernetes.io/docs/concepts/workloads/

### How do I report issues?

1. Check existing issues on GitHub
2. Create a minimal reproduction
3. Include:
   - Python version
   - Error messages
   - Steps to reproduce
   - Expected vs actual behavior

### Can I contribute?

Yes! Contributions are welcome:
1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Update documentation
5. Submit a pull request

---

[← Troubleshooting](troubleshooting.md) | [Documentation Home](README.md)