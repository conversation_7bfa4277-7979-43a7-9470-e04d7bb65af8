# Troubleshooting Guide

This guide helps you resolve common issues when working with the Python Application Template.

## Common Issues

### Application Won't Start

#### Port Already in Use

**Error:**
```
ERROR: [Errno 48] Address already in use
```

**Solution:**
```bash
# Find process using port 8000
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or use a different port
APP_PORT=8001 task run
```

#### Module Not Found

**Error:**
```
ModuleNotFoundError: No module named 'app'
```

**Solution:**
```bash
# Ensure you're in the correct directory
cd python-app-template

# Install dependencies
task install

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Database Connection Failed

**Error:**
```
sqlalchemy.exc.OperationalError: connection to server at "localhost", port 5432 failed
```

**Solution:**
```bash
# Start PostgreSQL
docker compose up -d postgres

# Check if PostgreSQL is running
docker compose ps

# Verify connection
docker compose exec postgres psql -U postgres -c "SELECT 1"

# Check DATABASE_URL
echo $DATABASE_URL
```

### Docker Issues

#### Docker Build Fails

**Error:**
```
ERROR: failed to solve: failed to fetch anonymous token
```

**Solution:**
```bash
# Clean Docker cache
docker system prune -a

# Build without cache
docker build --no-cache -t python-app .

# Check Docker daemon
docker info
```

#### Container Exits Immediately

**Error:**
```
Container exited with code 1
```

**Solution:**
```bash
# Check logs
docker logs <container-id>

# Run with interactive shell
docker run -it python-app:latest /bin/bash

# Check entrypoint
docker run --entrypoint /bin/bash -it python-app:latest
```

#### Permission Denied in Container

**Error:**
```
PermissionError: [Errno 13] Permission denied
```

**Solution:**
```dockerfile
# Fix in Dockerfile
RUN chown -R appuser:appuser /app
USER appuser

# Or run with user
docker run --user $(id -u):$(id -g) python-app:latest
```

### Kafka Issues

#### Kafka Connection Refused

**Error:**
```
kafka.errors.NoBrokersAvailable
```

**Solution:**
```bash
# Start Kafka
docker compose up -d kafka zookeeper

# Wait for Kafka to be ready
sleep 30

# Test connection
task kafka-test-local

# Check Kafka logs
docker compose logs kafka
```

#### Consumer Not Receiving Messages

**Problem:** Messages sent but not consumed

**Solution:**
```bash
# Check consumer group
docker compose exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --describe --group app-consumer-group

# Reset offset
docker compose exec kafka kafka-consumer-groups \
  --bootstrap-server localhost:9092 \
  --group app-consumer-group \
  --reset-offsets --to-earliest \
  --topic app-events --execute
```

#### SSL/SASL Authentication Failed

**Error:**
```
Authentication failed: Invalid username or password
```

**Solution:**
```bash
# Verify credentials
echo $KAFKA_SASL_USERNAME
echo $KAFKA_SASL_PASSWORD

# Check security protocol
echo $KAFKA_SECURITY_PROTOCOL

# Test with kafkacat
kafkacat -b localhost:9092 \
  -X security.protocol=SASL_SSL \
  -X sasl.mechanisms=SCRAM-SHA-512 \
  -X sasl.username=$KAFKA_SASL_USERNAME \
  -X sasl.password=$KAFKA_SASL_PASSWORD \
  -L
```

### Database Issues

#### Migration Errors

**Error:**
```
alembic.util.exc.CommandError: Can't locate revision identified by 'head'
```

**Solution:**
```bash
# Initialize migrations
alembic init alembic

# Create initial migration
alembic revision --autogenerate -m "Initial"

# Apply migrations
alembic upgrade head
```

#### Connection Pool Exhausted

**Error:**
```
sqlalchemy.exc.TimeoutError: QueuePool limit of size 5 overflow 10 reached
```

**Solution:**
```python
# Increase pool size in config
DB_POOL_SIZE=50
DB_MAX_OVERFLOW=20

# Or in code
engine = create_async_engine(
    DATABASE_URL,
    pool_size=50,
    max_overflow=20
)
```

#### SQLite Async Issues

**Error:**
```
sqlite3.ProgrammingError: SQLite objects created in a thread can only be used in that same thread
```

**Solution:**
```python
# For SQLite with async
engine = create_async_engine(
    "sqlite+aiosqlite:///./test.db",
    connect_args={"check_same_thread": False}
)
```

### Kubernetes Issues

#### Pod CrashLoopBackOff

**Problem:** Pod keeps restarting

**Solution:**
```bash
# Check logs
kubectl logs pod-name --previous

# Describe pod
kubectl describe pod pod-name

# Common fixes:
# 1. Check resource limits
# 2. Verify environment variables
# 3. Check health probes
# 4. Review startup commands
```

#### ImagePullBackOff

**Error:** Failed to pull image

**Solution:**
```bash
# Check image name
kubectl describe pod pod-name | grep Image

# For private registry
kubectl create secret docker-registry regcred \
  --docker-server=myregistry.com \
  --docker-username=user \
  --docker-password=pass

# Use secret in deployment
spec:
  imagePullSecrets:
  - name: regcred
```

#### Service Not Accessible

**Problem:** Can't reach the service

**Solution:**
```bash
# Check service
kubectl get svc
kubectl describe svc python-app

# Port forward for testing
kubectl port-forward svc/python-app 8000:80

# Check endpoints
kubectl get endpoints python-app

# Check ingress
kubectl describe ingress python-app
```

### Testing Issues

#### Async Test Failures

**Error:**
```
RuntimeError: Task attached to a different loop
```

**Solution:**
```python
# Use session-scoped event loop
@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
```

#### Database Tests Failing

**Error:**
```
FAILED - Connection refused
```

**Solution:**
```python
# Use in-memory SQLite for tests
@pytest.fixture
async def test_db():
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:"
    )
    # ... rest of fixture
```

#### Coverage Not Working

**Problem:** Coverage shows 0%

**Solution:**
```bash
# Install coverage
pip install pytest-cov

# Run with correct source
pytest --cov=app --cov-report=term

# Check .coveragerc
[run]
source = app
omit = */tests/*
```

## Performance Issues

### Slow Application Startup

**Problem:** Application takes long to start

**Solutions:**
1. **Lazy Loading:**
```python
# Don't import heavy modules at top level
def get_ml_model():
    import tensorflow as tf  # Import when needed
    return tf.load_model()
```

2. **Connection Pooling:**
```python
# Pre-create connections
engine = create_async_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_size=20
)
```

3. **Async Initialization:**
```python
@app.on_event("startup")
async def startup():
    # Initialize in parallel
    await asyncio.gather(
        init_database(),
        init_kafka(),
        init_cache()
    )
```

### High Memory Usage

**Problem:** Container uses too much memory

**Solutions:**
1. **Limit Workers:**
```bash
# Reduce Uvicorn workers
uvicorn app.main:app --workers 2
```

2. **Database Connections:**
```python
# Reduce pool size
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=5
```

3. **Garbage Collection:**
```python
import gc

@app.on_event("startup")
async def configure_gc():
    gc.set_threshold(700, 10, 10)
```

### Slow Database Queries

**Problem:** API responses are slow

**Solutions:**
1. **Add Indexes:**
```python
class Todo(Base):
    __tablename__ = "todos"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, index=True)  # Add index
    created_at = Column(DateTime, index=True)
```

2. **Use Eager Loading:**
```python
# Avoid N+1 queries
stmt = select(User).options(selectinload(User.todos))
```

3. **Pagination:**
```python
async def get_todos(skip: int = 0, limit: int = 100):
    return await db.execute(
        select(Todo).offset(skip).limit(limit)
    )
```

## Debugging Techniques

### Enable Debug Logging

```python
# Set in environment
DEBUG=true
LOG_LEVEL=DEBUG

# Or in code
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Interactive Debugging

```python
# Add breakpoint
import pdb; pdb.set_trace()

# For async code
import asyncio
asyncio.create_task(pdb.set_trace())

# Using IPython
from IPython import embed; embed()
```

### Remote Debugging

```python
# For VS Code
import debugpy
debugpy.listen(5678)
debugpy.wait_for_client()
```

### Database Query Logging

```python
# Enable SQL echo
engine = create_async_engine(
    DATABASE_URL,
    echo=True  # Log all SQL
)

# Or use logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

## Environment-Specific Issues

### Development vs Production

**Problem:** Works locally but not in production

**Checklist:**
1. Environment variables set correctly
2. Database migrations applied
3. Static files served properly
4. Security settings (CORS, CSRF)
5. Resource limits appropriate

### Configuration Issues

**Problem:** Settings not loading

**Debug Steps:**
```python
# Print all settings
from app.config import settings
print(settings.dict())

# Check environment
import os
print(os.environ)

# Validate settings
try:
    Settings()
except ValidationError as e:
    print(e)
```

## Health Check Debugging

### Failing Health Checks

```python
# Add detailed health check
@app.get("/health/debug")
async def debug_health():
    checks = {}
    
    # Database check
    try:
        await db.execute("SELECT 1")
        checks["database"] = "ok"
    except Exception as e:
        checks["database"] = str(e)
    
    # Kafka check
    try:
        await kafka_manager.health_check()
        checks["kafka"] = "ok"
    except Exception as e:
        checks["kafka"] = str(e)
    
    return checks
```

## Getting Help

### Collect Diagnostics

```bash
# System info
uname -a
python --version
pip list

# Docker info
docker version
docker compose version

# Kubernetes info
kubectl version
kubectl get events --sort-by='.lastTimestamp'

# Application logs
docker logs container-name
kubectl logs deployment/python-app
```

### Create Minimal Reproduction

```python
# minimal_repro.py
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine

async def test_connection():
    engine = create_async_engine("postgresql+asyncpg://...")
    async with engine.connect() as conn:
        result = await conn.execute("SELECT 1")
        print(result.scalar())

asyncio.run(test_connection())
```

### Where to Get Help

1. **Check Documentation:** Review relevant guides in this documentation
2. **Search Issues:** Check GitHub issues for similar problems
3. **Community Forums:** FastAPI, SQLAlchemy communities
4. **Stack Overflow:** Tag with `fastapi`, `sqlalchemy`, `python-asyncio`

---

[← Testing Guide](testing.md) | [Documentation Home](README.md) | [FAQ →](faq.md)