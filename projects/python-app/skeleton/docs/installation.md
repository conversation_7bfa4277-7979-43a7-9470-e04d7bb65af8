# Installation Guide

This guide will help you set up your development environment for the Python Application Template.

## System Requirements

### Required Software
- **Python**: 3.12 or higher
- **Docker**: 20.10 or higher
- **Docker Compose**: 2.0 or higher
- **Git**: For version control

### Recommended Software
- **mise**: For Python version management (optional but recommended)
- **kubectl**: For Kubernetes deployment
- **make**: For running Makefile commands

### Hardware Requirements
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: At least 2GB free space
- **CPU**: 2+ cores recommended for development

## Installation Steps

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd python-app-template
```

### 2. Set Up Python Environment

#### Option A: Using mise (Recommended)

If you have [mise](https://mise.jdx.dev/) installed:

```bash
# mise will automatically use Python 3.12 as specified in mise.toml
mise install
```

#### Option B: Using System Python

Ensure Python 3.12+ is installed:

```bash
python --version  # Should show Python 3.12 or higher
```

### 3. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate

# On Windows:
venv\Scripts\activate
```

### 4. Install Dependencies

```bash
# Install all Python dependencies
pip install -r requirements.txt

# Or use the task command
task install
```

### 5. Set Up Environment Variables

Create a `.env` file in the project root:

```bash
cp .env.example .env  # If example exists
# Or create manually:
touch .env
```

Add the following basic configuration:

```env
# Application Settings
APP_NAME=python-app-template
DEBUG=true

# Database
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/app_db

# Kafka (optional for local development)
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT
```

### 6. Install Task (Optional but Recommended)

Task is a task runner that simplifies common commands:

```bash
# On macOS with Homebrew
brew install go-task

# On Linux
sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d

# Verify installation
task --version
```

### 7. Verify Installation

Run these commands to verify everything is set up correctly:

```bash
# Check Python dependencies
pip list

# Check task commands are available
task --list

# Run a simple test
task test
```

## Docker Setup

### 1. Install Docker

Follow the official Docker installation guide for your OS:
- [Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
- [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
- [Docker Engine for Linux](https://docs.docker.com/engine/install/)

### 2. Verify Docker Installation

```bash
docker --version
docker compose version
```

### 3. Pull Required Images

```bash
# Pull base images
docker pull python:3.12-slim
docker pull postgres:15
```

## Database Setup

### Local PostgreSQL with Docker

The easiest way to run PostgreSQL locally:

```bash
# Start PostgreSQL using docker-compose
docker compose up -d postgres

# Verify it's running
docker compose ps
```

### Alternative: System PostgreSQL

If you prefer to install PostgreSQL directly:

```bash
# macOS with Homebrew
brew install postgresql@15
brew services start postgresql@15

# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql-15

# Create database
createdb app_db
```

## Kafka Setup (Optional)

For local Kafka development:

```bash
# Start Kafka using docker-compose
docker compose up -d kafka zookeeper

# Wait for Kafka to be ready (about 30 seconds)
sleep 30

# Test Kafka connection
task kafka-test-local
```

## IDE Setup

### VS Code (Recommended)

Install recommended extensions:
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.vscode-pylance",
    "ms-python.black-formatter",
    "charliermarsh.ruff",
    "tamasfe.even-better-toml"
  ]
}
```

### PyCharm

1. Open the project directory
2. Configure Python interpreter to use your virtual environment
3. Enable Django support if using templates

## Common Installation Issues

### Python Version Mismatch

If you get errors about Python version:
```bash
# Check your Python version
python --version

# If needed, install Python 3.12
# macOS with Homebrew
brew install python@3.12

# Ubuntu/Debian
sudo apt-get install python3.12
```

### Permission Errors

If you encounter permission errors:
```bash
# Use sudo for global installations (not recommended)
sudo pip install -r requirements.txt

# Better: ensure virtual environment is activated
which python  # Should point to venv/bin/python
```

### Docker Connection Issues

If Docker commands fail:
```bash
# Ensure Docker daemon is running
docker info

# On Linux, add user to docker group
sudo usermod -aG docker $USER
# Log out and back in for changes to take effect
```

## Next Steps

Now that your environment is set up, proceed to:
- [Quick Start](quickstart.md) - Run the application
- [Development Guide](development.md) - Start developing
- [Configuration](configuration.md) - Configure the application

---

[← Back to Documentation](README.md) | [Quick Start →](quickstart.md)