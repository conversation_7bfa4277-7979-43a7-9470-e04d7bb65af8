# Configuration Guide

This guide explains how to configure the Python Application Template for different environments and use cases.

## Configuration Overview

The application uses a hierarchical configuration system:

1. **Default Values**: Defined in `app/config.py`
2. **Environment Variables**: Override defaults
3. **`.env` File**: Local development overrides
4. **Docker/Kubernetes**: Runtime configuration

## Core Configuration

### Application Settings

```env
# Basic application configuration
APP_NAME=python-app-template
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# Server configuration
APP_HOST=0.0.0.0
APP_PORT=8000
WORKERS=1
```

### Database Configuration

```env
# PostgreSQL (default)
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/dbname

# PostgreSQL with SSL
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/dbname?ssl=require

# SQLite (for testing)
DATABASE_URL=sqlite+aiosqlite:///./test.db

# Database pool settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=0
DB_POOL_TIMEOUT=30
```

### Kafka Configuration

```env
# Basic Kafka settings
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_NAME=app-events
KAFKA_GROUP_ID=app-consumer-group
KAFKA_AUTO_CREATE_TOPICS=true

# Security settings
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_SASL_MECHANISM=SCRAM-SHA-512
KAFKA_SASL_USERNAME=your-username
KAFKA_SASL_PASSWORD=your-password

# SSL settings
KAFKA_SSL_CA_LOCATION=/path/to/ca-cert
KAFKA_SSL_CERT_LOCATION=/path/to/client-cert
KAFKA_SSL_KEY_LOCATION=/path/to/client-key

# Consumer settings
KAFKA_AUTO_OFFSET_RESET=earliest
KAFKA_ENABLE_AUTO_COMMIT=true
KAFKA_MAX_POLL_RECORDS=500
```

## Environment-Specific Configuration

### Development Environment

Create a `.env` file for local development:

```env
# .env for development
APP_NAME=python-app-dev
DEBUG=true
LOG_LEVEL=DEBUG

# Local database
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/app_dev

# Local Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=PLAINTEXT

# Development features
RELOAD=true
SHOW_SQL_QUERIES=true
```

### Testing Environment

```env
# .env.test
APP_NAME=python-app-test
DEBUG=true
LOG_LEVEL=WARNING

# Use SQLite for faster tests
DATABASE_URL=sqlite+aiosqlite:///./test.db

# Disable Kafka for unit tests
KAFKA_ENABLED=false
```

### Production Environment

```env
# Production settings (set via environment variables)
APP_NAME=python-app-prod
DEBUG=false
LOG_LEVEL=WARNING

# Production database
DATABASE_URL=postgresql+asyncpg://user:<EMAIL>:5432/app_prod

# Production Kafka
KAFKA_BOOTSTRAP_SERVERS=kafka1.prod:9092,kafka2.prod:9092,kafka3.prod:9092
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_SASL_USERNAME=${KAFKA_USERNAME}
KAFKA_SASL_PASSWORD=${KAFKA_PASSWORD}

# Performance tuning
WORKERS=4
DB_POOL_SIZE=50
```

## Configuration Management in Code

### Using the Settings Class

The `app/config.py` file defines all configuration options:

```python
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Application
    app_name: str = "Python App Template"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # Server
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    workers: int = 1
    
    # Database
    database_url: str
    db_pool_size: int = 20
    db_max_overflow: int = 0
    
    # Kafka
    kafka_bootstrap_servers: Optional[str] = None
    kafka_topic_name: str = "app-events"
    kafka_security_protocol: str = "PLAINTEXT"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()
```

### Accessing Configuration

```python
from app.config import settings

# Use settings in your code
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    debug=settings.debug
)

# Conditional logic based on settings
if settings.debug:
    app.add_middleware(DebugMiddleware)
```

## Advanced Configuration

### Feature Flags

```env
# Feature toggles
FEATURE_NEW_UI=true
FEATURE_ADVANCED_SEARCH=false
FEATURE_BETA_API=true
```

```python
# app/config.py
class Settings(BaseSettings):
    # Feature flags
    feature_new_ui: bool = False
    feature_advanced_search: bool = False
    feature_beta_api: bool = False

# Usage
if settings.feature_new_ui:
    return render_template("new_ui.html")
```

### Dynamic Configuration

For configuration that changes at runtime:

```python
import json
from app.config import settings

class DynamicConfig:
    def __init__(self):
        self._config = {}
        self.reload()
    
    def reload(self):
        """Reload configuration from external source."""
        # Example: Load from file, database, or API
        if settings.config_source == "file":
            with open("dynamic_config.json") as f:
                self._config = json.load(f)
    
    def get(self, key: str, default=None):
        return self._config.get(key, default)

dynamic_config = DynamicConfig()
```

### Secrets Management

#### Using Environment Variables

```bash
# Set sensitive values via environment
export DATABASE_PASSWORD=secret123
export API_KEY=xyz789
```

#### Using Docker Secrets

```yaml
# docker-compose.yml
services:
  app:
    secrets:
      - db_password
      - api_key
    environment:
      DATABASE_PASSWORD_FILE: /run/secrets/db_password
      API_KEY_FILE: /run/secrets/api_key

secrets:
  db_password:
    file: ./secrets/db_password.txt
  api_key:
    file: ./secrets/api_key.txt
```

#### Using Kubernetes Secrets

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  database-password: cGFzc3dvcmQxMjM=  # base64 encoded
  api-key: eHl6Nzg5                    # base64 encoded
```

```yaml
# deployment.yaml
env:
  - name: DATABASE_PASSWORD
    valueFrom:
      secretKeyRef:
        name: app-secrets
        key: database-password
```

## Configuration Validation

### Startup Validation

```python
# app/main.py
from app.config import settings

@app.on_event("startup")
async def validate_config():
    """Validate configuration on startup."""
    errors = []
    
    # Check required settings
    if not settings.database_url:
        errors.append("DATABASE_URL is required")
    
    if settings.kafka_enabled and not settings.kafka_bootstrap_servers:
        errors.append("KAFKA_BOOTSTRAP_SERVERS required when Kafka is enabled")
    
    if errors:
        logger.error(f"Configuration errors: {errors}")
        raise ValueError(f"Invalid configuration: {', '.join(errors)}")
```

### Runtime Validation

```python
from pydantic import validator

class Settings(BaseSettings):
    app_port: int
    
    @validator("app_port")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
```

## Configuration Best Practices

### 1. Security

- Never commit secrets to version control
- Use environment variables for sensitive data
- Rotate credentials regularly
- Use least-privilege access

### 2. Organization

```env
# Group related settings
# === Application ===
APP_NAME=myapp
APP_VERSION=1.0.0

# === Database ===
DATABASE_URL=postgresql://...
DATABASE_POOL_SIZE=20

# === Kafka ===
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_NAME=events
```

### 3. Documentation

```python
class Settings(BaseSettings):
    # Document each setting
    app_name: str = Field(
        default="Python App",
        description="Application name shown in UI"
    )
    
    db_pool_size: int = Field(
        default=20,
        description="Maximum number of database connections",
        ge=1,
        le=100
    )
```

### 4. Environment Separation

```bash
# Use different files for different environments
.env                # Development (gitignored)
.env.example       # Example configuration (committed)
.env.test          # Test configuration
.env.prod          # Production template
```

## Common Configuration Scenarios

### Running Without Kafka

```env
# Disable Kafka entirely
KAFKA_ENABLED=false
```

```python
# app/main.py
if settings.kafka_enabled:
    await kafka_manager.start()
```

### Using External Services

```env
# Redis cache
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# Elasticsearch
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=app-logs

# S3 storage
AWS_ACCESS_KEY_ID=xxx
AWS_SECRET_ACCESS_KEY=yyy
S3_BUCKET_NAME=my-app-files
```

### Multi-tenant Configuration

```python
class TenantSettings(BaseSettings):
    tenant_id: str
    tenant_database_url: str
    tenant_features: List[str] = []
    
    @classmethod
    def for_tenant(cls, tenant_id: str):
        # Load tenant-specific configuration
        return cls(
            tenant_id=tenant_id,
            tenant_database_url=f"postgresql://.../{tenant_id}",
            tenant_features=load_tenant_features(tenant_id)
        )
```

## Debugging Configuration Issues

### 1. Print Current Configuration

```python
# Add debug endpoint
@app.get("/debug/config")
async def debug_config():
    if not settings.debug:
        raise HTTPException(403, "Only available in debug mode")
    
    # Safely expose non-sensitive config
    return {
        "app_name": settings.app_name,
        "debug": settings.debug,
        "database_connected": bool(settings.database_url),
        "kafka_enabled": bool(settings.kafka_bootstrap_servers)
    }
```

### 2. Configuration Validation Script

```python
# scripts/validate_config.py
from app.config import settings

def validate_configuration():
    print("Validating configuration...")
    
    # Check database connection
    print(f"Database URL configured: {'✓' if settings.database_url else '✗'}")
    
    # Check Kafka settings
    if settings.kafka_bootstrap_servers:
        print(f"Kafka servers: {settings.kafka_bootstrap_servers}")
        print(f"Kafka security: {settings.kafka_security_protocol}")
    
    print("Configuration validation complete!")

if __name__ == "__main__":
    validate_configuration()
```

### 3. Environment Variable Debugging

```bash
# List all environment variables
env | grep -E "^(APP_|DATABASE_|KAFKA_)" | sort

# Check specific variable
echo $DATABASE_URL

# Debug in Python
import os
print(os.environ.get('DATABASE_URL', 'Not set'))
```

---

[← Architecture](architecture.md) | [Documentation Home](README.md) | [Database Integration →](database.md)