apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.component_id }}
  namespace: ${{ values.env }}
  description: ${{ values.description }}
  annotations:
    backstage.io/techdocs-ref: dir:.
    backstage.io/kubernetes-id: ${{ values.component_id }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
    gitlab.com/project-slug: ${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}
    argocd/app-name: ${{ values.component_id }}-python
    environment: ${{ values.env }}
  tags:
    - python
    - kubernetes
    - argocd
    - ${{ values.env }}
  links:
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}
      title: GitLab Repository
      icon: gitlab
    - url: https://${{ (values.repoUrl | parseRepoUrl).host }}/${{ (values.repoUrl | parseRepoUrl).owner }}/${{ (values.repoUrl | parseRepoUrl).repo }}/-/pipelines
      title: CI/CD Pipelines
      icon: dashboard
    - url: https://argocd.hub.${{ values.env }}.esc.esetrs.cz/applications/${{ values.component_id }}-python
      title: ArgoCD Application
      icon: dashboard
    - url: https://${{ values.appUrl }}.idp-test.${{ values.env }}.esc.esetrs.cz
      title: Application URL
      icon: web
spec:
  type: project
  lifecycle: experimental
  owner: ${{ values.owner }}
  system: ${{ values.env }}/python-applications
  providesApis:
    - ${{ values.component_id }}-api
  dependsOn:
    - component:${{ values.env }}/${{ values.kafkaClusterName }}
    - component:${{ values.env }}/${{ values.dbClusterName }}
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: ${{ values.component_id }}-api
  namespace: ${{ values.env }}
  description: API for ${{ values.description }}
  annotations:
    backstage.io/techdocs-ref: dir:.
spec:
  type: openapi
  lifecycle: experimental
  owner: ${{ values.owner }}
  system: ${{ values.env }}/python-applications
  definition: |
    openapi: 3.0.1
    info:
      title: ${{ values.component_id }} API
      description: ${{ values.description }}
      version: 1.0.0
    servers:
      - url: https://${{ values.component_id }}.${{ values.namespace }}.${{ values.env }}.example.com
    paths:
      /health:
        get:
          summary: Health check endpoint
          tags:
            - System
          responses:
            '200':
              description: Service is healthy
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      status:
                        type: string
                        example: healthy
      /api/todos:
        get:
          summary: Get all todos
          tags:
            - Todos
          responses:
            '200':
              description: List of todos
              content:
                application/json:
                  schema:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        title:
                          type: string
                        completed:
                          type: boolean
        post:
          summary: Create a new todo
          tags:
            - Todos
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    title:
                      type: string
                    completed:
                      type: boolean
          responses:
            '201':
              description: Todo created
      /api/kafka/send:
        post:
          summary: Send message to Kafka
          tags:
            - Kafka
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    message:
                      type: string
          responses:
            '200':
              description: Message sent successfully