# Python Web Application

A modern Python web application featuring Kafka messaging and todo management with PostgreSQL persistence.

## Features

- **Web UI**: 3 pages with modern Bootstrap design
  - Main page with navigation links
  - Kafka page for sending/receiving messages
  - Todo management page with CRUD operations
- **Kafka Integration**: Producer/Consumer with TLS and client certificate support
- **Database**: PostgreSQL with automatic schema creation
- **Configuration**: Environment-based configuration management
- **Testing**: Comprehensive test suite with pytest
- **Docker**: Full Docker Compose setup for easy deployment

## Quick Start

### Using Docker Compose (Recommended)

1. Clone and navigate to the project:
```bash
cd python-app-template
```

2. Copy the environment file:
```bash
cp .env.example .env
```

3. Start all services:
```bash
docker-compose up -d
```

4. Access the application at http://localhost:8000

### Local Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up PostgreSQL and Kafka (or use Docker Compose for infrastructure only):
```bash
docker-compose up -d postgres kafka zookeeper
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your settings
```

4. Run the application:
```bash
python run.py
```

## Configuration

All configuration is handled through environment variables. See `.env.example` for available options:

- **Database**: PostgreSQL connection settings
- **Kafka**: Bootstrap servers, topics, and security settings (TLS/SASL)
- **Application**: Host, port, and debug settings

### Kafka Security

The application supports multiple Kafka security configurations:

- **PLAINTEXT**: No authentication (default)
- **SSL**: TLS encryption with client certificates
- **SASL_SSL**: SASL authentication with TLS

## Testing

Run the test suite:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=app
```

## API Endpoints

- `GET /`: Main page
- `GET /kafka`: Kafka messaging page  
- `POST /kafka/send`: Send message to Kafka
- `GET /todos`: Todo management page
- `POST /todos/add`: Add new todo
- `POST /todos/delete/{id}`: Delete todo

## Architecture

- **FastAPI**: Modern async web framework
- **SQLAlchemy**: ORM with async support
- **kafka-python**: Kafka client with full feature support
- **Jinja2**: Template engine for server-side rendering
- **Bootstrap 5**: Modern responsive UI framework

## Development

The application uses Python 3.12 and mise for environment management. The database schema is automatically created on startup, expecting an empty database.

For local development, the application supports hot reload when `DEBUG=true` is set.