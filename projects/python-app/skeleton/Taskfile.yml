version: '3'

vars:
  DOCKER_IMAGE: escdev.azurecr.io/apps/python-int-demo
  DOCKER_TAG: '{{.DOCKER_TAG | default "latest"}}'
  PYTHON_VERSION: '3.12'

tasks:
  default:
    desc: Show available tasks
    cmds:
      - task --list

  install:
    desc: Install Python dependencies
    cmds:
      - pip install -r requirements.txt

  run:
    desc: Run the application locally
    cmds:
      - python run.py
    env:
      DATABASE_URL: sqlite+aiosqlite:///./test_app.db
      DEBUG: "true"

  run-docker:
    desc: Run application with Docker Compose
    cmds:
      - docker-compose up -d
      - echo "Application running at http://localhost:8000"
      - echo "Run 'task logs' to see logs"

  stop-docker:
    desc: Stop Docker Compose services
    cmds:
      - docker-compose down

  logs:
    desc: Show Docker Compose logs
    cmds:
      - docker-compose logs -f

  test:
    desc: Run all tests
    cmds:
      - python -m pytest tests/ -v

  test-coverage:
    desc: Run tests with coverage
    cmds:
      - python -m pytest tests/ --cov=app --cov-report=html --cov-report=term

  lint:
    desc: Run linting checks
    cmds:
      - echo "Running flake8..."
      - flake8 app/ tests/ || true
      - echo "Running mypy..."
      - mypy app/ || true

  docker-build:
    desc: Build Docker image for multiple architectures
    cmds:
      - echo "Building multi-arch Docker image..."
      - docker buildx build --platform linux/amd64,linux/arm64 -t {{.DOCKER_IMAGE}}:{{.DOCKER_TAG}} .

  docker-build-push:
    desc: Build and push multi-arch Docker image
    cmds:
      - echo "Building and pushing multi-arch Docker image..."
      - docker buildx build --platform linux/amd64,linux/arm64 -t {{.DOCKER_IMAGE}}:{{.DOCKER_TAG}} --push .

  docker-setup-buildx:
    desc: Setup Docker buildx for multi-arch builds
    cmds:
      - docker buildx create --name multiarch-builder --use || true
      - docker buildx inspect --bootstrap
      - echo "Buildx is ready for multi-arch builds"

  k8s-deploy:
    desc: Deploy using custom overlay (default dev)
    vars:
      OVERLAY: '{{.OVERLAY | default "dev"}}'
    cmds:
      - kubectl apply -k deploy/overlays/{{.OVERLAY}}/

  k8s-deploy-kafka:
    desc: Deploy with Kafka integration
    cmds:
      - kubectl apply -k deploy/overlays/dev/
      - kubectl rollout status deployment/kafka-python-app -n python-app --timeout=300s

  k8s-delete:
    desc: Delete deployment using custom overlay (default dev)
    vars:
      OVERLAY: '{{.OVERLAY | default "dev"}}'
    cmds:
      - kubectl delete -k deploy/overlays/{{.OVERLAY}}/ || true

  k8s-delete-kafka:
    desc: Delete Kafka deployment
    cmds:
      - kubectl delete -k deploy/overlays/dev/ || true

  k8s-logs:
    desc: Show application logs
    vars:
      NAMESPACE: '{{.NAMESPACE | default "python-app"}}'
    cmds:
      - kubectl logs -l app=python-app -n {{.NAMESPACE}} -f --tail=100

  k8s-status:
    desc: Show deployment status
    vars:
      NAMESPACE: '{{.NAMESPACE | default "python-app"}}'
    cmds:
      - kubectl get all -l app=python-app -n {{.NAMESPACE}}
      - kubectl get ingress -l app=python-app -n {{.NAMESPACE}}

  db-migrate:
    desc: Run database migrations (creates tables)
    cmds:
      - python -c "import asyncio; from app.database import create_tables; asyncio.run(create_tables())"

  kafka-test-local:
    desc: Test Kafka connection locally
    cmds:
      - python -c "from app.kafka_manager import kafka_manager; import asyncio; asyncio.run(kafka_manager.send_message('Test message from Taskfile'))"

  ci:
    desc: Run CI pipeline (test, lint, build)
    cmds:
      - task: test
      - task: lint
      - task: docker-build

  deploy-all:
    desc: Build and deploy to Kafka environment
    cmds:
      - task: docker-build-push
      - task: k8s-deploy-kafka

  local-dev:
    desc: Setup local development environment
    cmds:
      - task: install
      - task: db-migrate
      - task: run

  run-hybrid:
    desc: Run app locally with Kafka and DB in Docker
    deps:
      - install
    cmds:
      - docker-compose up -d zookeeper kafka postgres
      - echo "Waiting for services to be ready..."
      - sleep 5
      - python run.py
    env:
      DATABASE_URL: postgresql+asyncpg://user:password@localhost:5432/app_db
      KAFKA_BOOTSTRAP_SERVERS: localhost:9092
      KAFKA_TOPIC: app-messages
      DEBUG: "true"

  stop-hybrid:
    desc: Stop Docker services used in hybrid mode
    cmds:
      - docker-compose stop zookeeper kafka postgres
      - docker-compose rm -f zookeeper kafka postgres

  logs-hybrid:
    desc: Show logs for Docker services in hybrid mode
    cmds:
      - docker-compose logs -f zookeeper kafka postgres

  clean:
    desc: Clean up generated files
    cmds:
      - rm -rf .pytest_cache
      - rm -rf htmlcov
      - rm -rf .coverage
      - find . -type d -name __pycache__ -exec rm -rf {} + || true
      - rm -f test_app.db

  check-deps:
    desc: Check for outdated dependencies
    cmds:
      - pip list --outdated

  update-deps:
    desc: Update all dependencies
    cmds:
      - pip install --upgrade -r requirements.txt

  k8s-check-kafka-secret:
    desc: Check if Kafka secret exists
    vars:
      NAMESPACE: '{{.NAMESPACE | default "python-app"}}'
      SECRET_NAME: '{{.SECRET_NAME | default "user"}}'
    cmds:
      - |
        if [ -z "{{.NAMESPACE}}" ]; then
          echo "❌ ERROR: NAMESPACE is required"
          echo "   Usage: task k8s-check-kafka-secret NAMESPACE=python-app"
          exit 1
        fi
      - kubectl get secret {{.SECRET_NAME}} -n {{.NAMESPACE}} || echo "Secret '{{.SECRET_NAME}}' not found in namespace {{.NAMESPACE}}"

  k8s-copy-kafka-secret:
    desc: Copy Kafka secret from source namespace
    vars:
      SOURCE_NS: '{{.SOURCE_NS | default "kafka-cluster-hub"}}'
      TARGET_NS: '{{.TARGET_NS | default "python-app"}}'
      SECRET_NAME: '{{.SECRET_NAME | default "user"}}'
    cmds:
      - |
        if [ -z "{{.TARGET_NS}}" ]; then
          echo "❌ ERROR: TARGET_NS is required"
          echo "   Usage: task k8s-copy-kafka-secret TARGET_NS=python-app"
          exit 1
        fi
      - |
        kubectl get secret {{.SECRET_NAME}} -n {{.SOURCE_NS}} -o yaml | \
        sed 's/namespace: {{.SOURCE_NS}}/namespace: {{.TARGET_NS}}/' | \
        kubectl apply -f -