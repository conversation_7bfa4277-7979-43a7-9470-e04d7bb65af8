stages:
  - build
  - test
  - publish
  - docker

variables:
  ENTITY_NAMESPACE: ${{ values.env }}
  ENTITY_KIND: "Component"
  ENTITY_NAME: ${{ values.component_id }}
  IMAGE_NAME: apps/${{ values.projectName | lower }}
  PYTHON_VERSION: ${{ values.pythonVersion }}

default:
  image: python:${{ values.pythonVersion }}-slim

# -----------------------------------------------------------
# Stage 1: Build Docs
# -----------------------------------------------------------
build-docs:
  stage: build
  image: node:18-bullseye
  only:
    - main
  before_script:
    - apt-get update
    - apt-get install -y python3 python3-pip curl graphviz default-jdk
  script:
    # 1. Install needed CLIs and dependencies
    - npm install -g @techdocs/cli
    - python3 -m pip install --upgrade pip
    - python3 -m pip install mkdocs-techdocs-core==1.*

    # 2. Generate docs site
    - techdocs-cli generate --no-docker --verbose

  artifacts:
    paths:
      - site
    expire_in: 1 day

# -----------------------------------------------------------
# Stage 2: Publish Docs
# -----------------------------------------------------------
publish-docs:
  stage: publish
  image: node:18-bullseye
  only:
    - main
  dependencies:
    - build-docs
  before_script:
    - apt-get update
    - apt-get install -y python3 python3-pip
    - npm install -g @techdocs/cli
  script:
    # Publish to Azure Blob Storage
    - techdocs-cli publish --publisher-type azureBlobStorage --storage-name "$TECHDOCS_AZURE_BLOB_CONTAINER" --azureAccountName "$TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_NAME" --azureAccountKey "$TECHDOCS_AZURE_BLOB_STORAGE_ACCOUNT_KEY" --entity "$ENTITY_NAMESPACE/$ENTITY_KIND/$ENTITY_NAME"

# -----------------------------------------------------------
# Stage 3: Test stage - run tests
# -----------------------------------------------------------
# 
test:
  stage: test
  image: python:3.12-slim
  before_script:
    - pip install -r requirements.txt
    - pip install pytest pytest-cov pytest-asyncio
  script:
    - python -m pytest tests/ --cov=app --cov-report=html --cov-report=term
  coverage: '/(?i)total.*? (100(?:\.0+)?\s*%|[1-9]?\d(?:\.\d+)?\s*%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov/
    expire_in: 1 week


# -----------------------------------------------------------
# Stage 4: Build and Push Docker Image
# -----------------------------------------------------------
docker-build-push:
  stage: docker
  image: docker:28.2.2
  services:
    - docker:28.2.2-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - apk add --no-cache git
    - COMMIT_SHA=$(git rev-parse --short=6 HEAD)
    - VERSION_TAG="$CI_COMMIT_REF_NAME-$COMMIT_SHA"
    - echo "Building image with version $VERSION_TAG"
    # Login to ACR
    - echo "$ACR_PASSWORD" | docker login $ACR_REGISTRY -u $ACR_USERNAME --password-stdin
  script:
    # Build and push Docker image
    - docker build -t $ACR_REGISTRY/$IMAGE_NAME:latest -t $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG .
    - docker push $ACR_REGISTRY/$IMAGE_NAME:latest
    - docker push $ACR_REGISTRY/$IMAGE_NAME:$VERSION_TAG
  only:
    - main