from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Integer, String, DateTime, func
from datetime import datetime
import os
from .config import settings


class Base(DeclarativeBase):
    pass


class Todo(Base):
    __tablename__ = "todos"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    title: Mapped[str] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())


def create_engine():
    """Create engine with proper settings for SQLite or PostgreSQL"""
    db_url = settings.database_url
    if db_url.startswith("sqlite"):
        return create_async_engine(db_url, echo=settings.debug, connect_args={"check_same_thread": False})
    else:
        return create_async_engine(db_url, echo=settings.debug)


engine = create_engine()
async_session_maker = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def get_db():
    async with async_session_maker() as session:
        yield session


async def create_tables():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)