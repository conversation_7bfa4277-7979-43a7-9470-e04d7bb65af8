{% extends "base.html" %}

{% block title %}Home - Python Web App{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center">
                <h1 class="card-title mb-4">
                    <i class="fas fa-rocket text-primary me-3"></i>
                    Welcome to Python Web App
                </h1>
                <p class="card-text lead mb-5">
                    A modern web application featuring Kafka messaging and todo management.
                </p>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="text-center mb-3">
                                    <i class="fas fa-stream fa-3x text-primary"></i>
                                </div>
                                <h5 class="card-title">Kafka Messaging</h5>
                                <p class="card-text flex-grow-1">
                                    Send and receive messages through Kafka topics with real-time updates.
                                </p>
                                <a href="/kafka" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>Go to Kafka
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="text-center mb-3">
                                    <i class="fas fa-tasks fa-3x text-success"></i>
                                </div>
                                <h5 class="card-title">Todo Management</h5>
                                <p class="card-text flex-grow-1">
                                    Manage your tasks with a simple and intuitive todo list interface.
                                </p>
                                <a href="/todos" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>Go to Todos
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}