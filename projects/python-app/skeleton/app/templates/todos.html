{% extends "base.html" %}

{% block title %}Todos - Python Web App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Add New Todo
                </h5>
            </div>
            <div class="card-body">
                <form method="post" action="/todos/add">
                    <div class="mb-3">
                        <label for="title" class="form-label">Todo Title</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               placeholder="Enter todo title..." required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>Add Todo
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Your Todos
                    <span class="badge bg-light text-dark ms-2">{{ todos|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if todos %}
                    <div class="todos-container">
                        {% for todo in todos %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-3">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ todo.title }}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Created: {{ todo.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </small>
                            </div>
                            <form method="post" action="/todos/delete/{{ todo.id }}" class="ms-3">
                                <button type="submit" class="btn btn-outline-danger btn-sm" 
                                        onclick="return confirm('Are you sure you want to delete this todo?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                        <p>No todos yet. Add your first todo to get started!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}