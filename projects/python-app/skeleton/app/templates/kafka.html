{% extends "base.html" %}

{% block title %}Kafka - Python Web App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-paper-plane me-2"></i>Send Message
                </h5>
            </div>
            <div class="card-body">
                <form method="post" action="/kafka/send">
                    <div class="mb-3">
                        <label for="message" class="form-label">Message</label>
                        <textarea class="form-control" id="message" name="message" rows="3" 
                                placeholder="Enter your message here..." required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-send me-2"></i>Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-inbox me-2"></i>Received Messages
                </h5>
                <div>
                    <button class="btn btn-sm btn-light me-2" onclick="refreshManually()">
                        <i class="fas fa-refresh me-1"></i>Refresh
                    </button>
                    <form method="post" action="/kafka/clear" style="display: inline;">
                        <button type="submit" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash me-1"></i>Clear All
                        </button>
                    </form>
                </div>
            </div>
            <div class="card-body">
                {% include 'kafka_messages.html' %}
            </div>
        </div>
    </div>
</div>

<style>
.messages-container::-webkit-scrollbar {
    width: 8px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>
{% endblock %}

{% block scripts %}
<script>
    let refreshInterval;
    let isTyping = false;
    
    // Track when user is typing in the message textarea
    const messageTextarea = document.getElementById('message');
    messageTextarea.addEventListener('focus', function() {
        isTyping = true;
        stopAutoRefresh();
    });
    
    messageTextarea.addEventListener('blur', function() {
        isTyping = false;
        // Resume auto-refresh after a short delay
        setTimeout(() => {
            if (!isTyping) {
                startAutoRefresh();
            }
        }, 1000);
    });
    
    // Function to refresh only the messages container
    async function refreshMessages() {
        if (isTyping) return;
        
        try {
            const response = await fetch('/kafka/messages', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const html = await response.text();
                const messagesContainer = document.querySelector('.col-md-6:last-child .card-body');
                if (messagesContainer) {
                    messagesContainer.innerHTML = html;
                }
            }
        } catch (error) {
            console.error('Error refreshing messages:', error);
        }
    }
    
    // Start auto-refresh
    function startAutoRefresh() {
        if (!refreshInterval) {
            refreshInterval = setInterval(refreshMessages, 5000);
        }
    }
    
    // Stop auto-refresh
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }
    
    // Manual refresh button
    window.refreshManually = function() {
        refreshMessages();
    }
    
    // Start auto-refresh when page loads
    startAutoRefresh();
    
    // Handle form submission via AJAX to avoid page reload
    const sendForm = document.querySelector('form[action="/kafka/send"]');
    sendForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(sendForm);
        
        try {
            const response = await fetch('/kafka/send', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                // Clear the message field
                messageTextarea.value = '';
                // Immediately refresh messages
                await refreshMessages();
                // Show success feedback
                const button = sendForm.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-2"></i>Sent!';
                button.classList.add('btn-success');
                button.classList.remove('btn-primary');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.add('btn-primary');
                    button.classList.remove('btn-success');
                }, 2000);
            } else {
                alert('Failed to send message. Please try again.');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('Error sending message. Please try again.');
        }
    });
    
    // Handle clear all form submission via AJAX
    const clearForm = document.querySelector('form[action="/kafka/clear"]');
    clearForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to clear all messages?')) {
            return;
        }
        
        try {
            const response = await fetch('/kafka/clear', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                // Immediately refresh messages
                await refreshMessages();
                // Show success feedback
                const button = clearForm.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-1"></i>Cleared!';
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            } else {
                alert('Failed to clear messages. Please try again.');
            }
        } catch (error) {
            console.error('Error clearing messages:', error);
            alert('Error clearing messages. Please try again.');
        }
    });
</script>
{% endblock %}