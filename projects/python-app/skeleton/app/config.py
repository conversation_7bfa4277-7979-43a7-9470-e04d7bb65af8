from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # Database
    database_url: str = os.getenv("DATABASE_URL", "postgresql+asyncpg://user:password@localhost:5432/app_db")
    
    # Kafka
    kafka_bootstrap_servers: str = "localhost:9092"
    kafka_topic: str = "app-messages"
    kafka_security_protocol: str = "PLAINTEXT"  # or SSL, SASL_SSL
    kafka_ssl_cafile: Optional[str] = None
    kafka_ssl_certfile: Optional[str] = None
    kafka_ssl_keyfile: Optional[str] = None
    kafka_ssl_password: Optional[str] = None
    kafka_sasl_mechanism: Optional[str] = None
    kafka_sasl_plain_username: Optional[str] = None
    kafka_sasl_plain_password: Optional[str] = None
    
    # App
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    debug: bool = False

    model_config = {
        "env_file": ".env"
    }


settings = Settings()