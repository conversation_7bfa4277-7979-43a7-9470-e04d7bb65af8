import asyncio
import json
import logging
import ssl
from typing import List, Dict, Any, Optional
try:
    from kafka import KafkaProducer, KafkaConsumer
    from kafka.errors import KafkaError
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False
    KafkaError = Exception
from .config import settings

try:
    from kafka.admin import KafkaAdminClient, NewTopic
    from kafka.errors import TopicAlreadyExistsError
except ImportError:
    KafkaAdminClient = None
    NewTopic = None
    TopicAlreadyExistsError = None

logger = logging.getLogger(__name__)


class KafkaManager:
    def __init__(self):
        self.producer: Optional[KafkaProducer] = None
        self.consumer: Optional[KafkaConsumer] = None
        self.messages: List[Dict[str, Any]] = []
        self.max_messages = 100
        self._topic_created = False
        
    def _get_kafka_config(self) -> Dict[str, Any]:
        config = {
            'bootstrap_servers': settings.kafka_bootstrap_servers.split(','),
            'security_protocol': settings.kafka_security_protocol,
        }
        
        if settings.kafka_security_protocol in ['SSL', 'SASL_SSL']:
            if settings.kafka_ssl_cafile:
                config['ssl_cafile'] = settings.kafka_ssl_cafile
            if settings.kafka_ssl_certfile:
                config['ssl_certfile'] = settings.kafka_ssl_certfile
            if settings.kafka_ssl_keyfile:
                config['ssl_keyfile'] = settings.kafka_ssl_keyfile
            if settings.kafka_ssl_password:
                config['ssl_password'] = settings.kafka_ssl_password
            # Disable hostname verification for self-signed certificates
            config['ssl_check_hostname'] = False
            # Create SSL context that doesn't verify certificates
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            # Load the client certificate and key
            if settings.kafka_ssl_certfile and settings.kafka_ssl_keyfile:
                ssl_context.load_cert_chain(
                    certfile=settings.kafka_ssl_certfile,
                    keyfile=settings.kafka_ssl_keyfile,
                    password=settings.kafka_ssl_password
                )
            # Load the CA certificate
            if settings.kafka_ssl_cafile:
                ssl_context.load_verify_locations(cafile=settings.kafka_ssl_cafile)
            config['ssl_context'] = ssl_context
                
        if settings.kafka_security_protocol in ['SASL_PLAINTEXT', 'SASL_SSL']:
            if settings.kafka_sasl_mechanism:
                config['sasl_mechanism'] = settings.kafka_sasl_mechanism
            if settings.kafka_sasl_plain_username:
                config['sasl_plain_username'] = settings.kafka_sasl_plain_username
            if settings.kafka_sasl_plain_password:
                config['sasl_plain_password'] = settings.kafka_sasl_plain_password
                
        return config
    
    def create_topic_if_needed(self):
        """Create Kafka topic if it doesn't exist"""
        if self._topic_created or not KAFKA_AVAILABLE or not KafkaAdminClient:
            return
            
        try:
            admin_config = self._get_kafka_config()
            admin_client = KafkaAdminClient(**admin_config)
            
            topic = NewTopic(
                name=settings.kafka_topic,
                num_partitions=1,
                replication_factor=1
            )
            
            logger.info(f"Attempting to create Kafka topic: {settings.kafka_topic}")
            admin_client.create_topics([topic])
            logger.info(f"Successfully created Kafka topic: {settings.kafka_topic}")
            self._topic_created = True
            
        except TopicAlreadyExistsError:
            logger.info(f"Kafka topic already exists: {settings.kafka_topic}")
            self._topic_created = True
        except Exception as e:
            logger.error(f"Failed to create Kafka topic: {type(e).__name__}: {e}")
        finally:
            try:
                admin_client.close()
            except:
                pass
    
    def get_producer(self):
        if not KAFKA_AVAILABLE:
            logger.error("Kafka library not available - install kafka-python")
            return None
        
        # Try to create topic first
        self.create_topic_if_needed()
        
        if self.producer is None:
            try:
                config = self._get_kafka_config()
                config.update({
                    'value_serializer': lambda v: json.dumps(v).encode('utf-8'),
                    'key_serializer': lambda k: k.encode('utf-8') if k else None,
                    'acks': 'all',
                    'retries': 3,
                    'max_block_ms': 5000,  # Wait max 5 seconds for connection
                })
                logger.info(f"Creating Kafka producer with servers: {config['bootstrap_servers']}")
                self.producer = KafkaProducer(**config)
                logger.info("Kafka producer created successfully")
            except Exception as e:
                logger.error(f"Failed to create Kafka producer: {type(e).__name__}: {e}")
                logger.error(f"Bootstrap servers: {settings.kafka_bootstrap_servers}")
                return None
        return self.producer
    
    def get_consumer(self):
        if not KAFKA_AVAILABLE:
            logger.error("Kafka library not available - install kafka-python")
            return None
            
        # Try to create topic first
        self.create_topic_if_needed()
        
        if self.consumer is None:
            try:
                config = self._get_kafka_config()
                config.update({
                    'value_deserializer': lambda m: json.loads(m.decode('utf-8')),
                    'auto_offset_reset': 'latest',
                    'enable_auto_commit': True,
                    'group_id': 'webapp-group',
                    'consumer_timeout_ms': 1000,  # Don't block forever
                })
                logger.info(f"Creating Kafka consumer for topic: {settings.kafka_topic}")
                self.consumer = KafkaConsumer(settings.kafka_topic, **config)
                logger.info("Kafka consumer created successfully")
            except Exception as e:
                logger.error(f"Failed to create Kafka consumer: {type(e).__name__}: {e}")
                logger.error(f"Topic: {settings.kafka_topic}, Servers: {settings.kafka_bootstrap_servers}")
                return None
        return self.consumer
    
    async def send_message(self, message: str) -> bool:
        try:
            producer = self.get_producer()
            if producer is None:
                logger.warning("Kafka producer not available - message not sent")
                return False
            
            import datetime
            logger.info(f"Sending message to Kafka topic '{settings.kafka_topic}': {message[:50]}...")
            msg_data = {
                'message': message, 
                'timestamp': datetime.datetime.now().isoformat(),
                'source': 'python-app-template'
            }
            future = producer.send(settings.kafka_topic, msg_data)
            
            # Wait for message to be sent
            record_metadata = future.get(timeout=10)
            logger.info(f"Message sent successfully to partition {record_metadata.partition} at offset {record_metadata.offset}")
            
            producer.flush()
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {type(e).__name__}: {e}")
            return False
    
    async def start_consuming(self):
        def consume():
            try:
                consumer = self.get_consumer()
                if consumer is None:
                    logger.warning("Kafka consumer not available - no messages will be consumed")
                    return
                
                logger.info("Kafka consumer loop started")
                while True:
                    try:
                        # Poll for messages with timeout
                        msg_pack = consumer.poll(timeout_ms=1000)
                        
                        for tp, messages in msg_pack.items():
                            for message in messages:
                                import datetime
                                msg_data = {
                                    'id': f"{tp.topic}-{tp.partition}-{message.offset}",
                                    'message': message.value.get('message', ''),
                                    'timestamp': message.value.get('timestamp', ''),
                                    'source': message.value.get('source', 'unknown'),
                                    'offset': message.offset,
                                    'partition': message.partition,
                                    'topic': message.topic,
                                    'key': message.key.decode('utf-8') if message.key else None,
                                    'received_at': datetime.datetime.now().isoformat()
                                }
                                self.messages.append(msg_data)
                                if len(self.messages) > self.max_messages:
                                    self.messages.pop(0)
                                logger.info(f"Received message from Kafka: topic={message.topic}, partition={message.partition}, offset={message.offset}")
                    except Exception as e:
                        if "StopIteration" not in str(e):
                            logger.error(f"Error polling messages: {e}")
                        break
                        
            except Exception as e:
                logger.error(f"Consumer error: {type(e).__name__}: {e}")
        
        if KAFKA_AVAILABLE:
            logger.info("Starting Kafka consumer in background")
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, consume)
        else:
            logger.info("Kafka not available - skipping consumer startup")
    
    def get_messages(self) -> List[Dict[str, Any]]:
        return self.messages.copy()
    
    def delete_message(self, message_id: str) -> bool:
        """Delete a message by its ID"""
        try:
            self.messages = [msg for msg in self.messages if msg.get('id') != message_id]
            logger.info(f"Deleted message with ID: {message_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete message: {e}")
            return False
    
    def clear_messages(self) -> bool:
        """Clear all messages"""
        try:
            self.messages.clear()
            logger.info("Cleared all messages")
            return True
        except Exception as e:
            logger.error(f"Failed to clear messages: {e}")
            return False
    
    def close(self):
        if self.producer:
            self.producer.close()
        if self.consumer:
            self.consumer.close()


kafka_manager = KafkaManager()