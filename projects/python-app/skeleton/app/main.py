from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from contextlib import asynccontextmanager
import asyncio
import logging

from .database import get_db, create_tables, Todo
from .kafka_manager import kafka_manager
from .config import settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    await create_tables()
    
    consumer_task = asyncio.create_task(kafka_manager.start_consuming())
    
    yield
    
    consumer_task.cancel()
    try:
        await consumer_task
    except asyncio.CancelledError:
        pass
    kafka_manager.close()


app = FastAPI(title="Python Web App", lifespan=lifespan)
templates = Jinja2Templates(directory="app/templates")

try:
    app.mount("/static", StaticFiles(directory="app/static"), name="static")
except RuntimeError:
    pass


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/kafka", response_class=HTMLResponse)
async def kafka_page(request: Request):
    messages = kafka_manager.get_messages()
    return templates.TemplateResponse("kafka.html", {"request": request, "messages": messages})


@app.get("/kafka/messages", response_class=HTMLResponse)
async def kafka_messages(request: Request):
    """Return just the messages section for AJAX refresh"""
    messages = kafka_manager.get_messages()
    return templates.TemplateResponse("kafka_messages.html", {"request": request, "messages": messages})


@app.post("/kafka/send")
async def send_kafka_message(request: Request, message: str = Form(...)):
    success = await kafka_manager.send_message(message)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to send message")
    
    # Check if this is an AJAX request
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        # Return a simple success response for AJAX
        return {"status": "success"}
    
    return RedirectResponse(url="/kafka", status_code=303)


@app.post("/kafka/delete/{message_id}")
async def delete_kafka_message(message_id: str):
    success = kafka_manager.delete_message(message_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete message")
    return RedirectResponse(url="/kafka", status_code=303)


@app.post("/kafka/clear")
async def clear_kafka_messages(request: Request):
    success = kafka_manager.clear_messages()
    if not success:
        raise HTTPException(status_code=500, detail="Failed to clear messages")
    
    # Check if this is an AJAX request
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        # Return a simple success response for AJAX
        return {"status": "success"}
    
    return RedirectResponse(url="/kafka", status_code=303)


@app.get("/todos", response_class=HTMLResponse)
async def todos_page(request: Request, db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(Todo).order_by(Todo.created_at.desc()))
    todos = result.scalars().all()
    return templates.TemplateResponse("todos.html", {"request": request, "todos": todos})


@app.post("/todos/add")
async def add_todo(title: str = Form(...), db: AsyncSession = Depends(get_db)):
    if not title.strip():
        raise HTTPException(status_code=400, detail="Todo title cannot be empty")
    
    todo = Todo(title=title.strip())
    db.add(todo)
    await db.commit()
    return RedirectResponse(url="/todos", status_code=303)


@app.post("/todos/delete/{todo_id}")
async def delete_todo(todo_id: int, db: AsyncSession = Depends(get_db)):
    await db.execute(delete(Todo).where(Todo.id == todo_id))
    await db.commit()
    return RedirectResponse(url="/todos", status_code=303)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.app_host, port=settings.app_port)