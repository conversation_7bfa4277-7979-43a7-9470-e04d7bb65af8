apiVersion: v1
kind: Service
metadata:
  name: ${{ values.name }}
  namespace: ${{ values.namespace }}
  labels:
    app: ${{ values.name }}
    backstage.io/kubernetes-id: ${{ values.name }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
spec:
  selector:
    app: ${{ values.name }}
  ports:
    - name: mongodb
      protocol: TCP
      port: 27017
      targetPort: 27017
  type: ClusterIP
