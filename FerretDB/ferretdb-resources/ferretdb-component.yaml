apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: "${{ values.name }}"
  title: "${{ values.name }}"
  namespace: "${{ values.env }}"
  annotations:
    backstage.io/kubernetes-id: "${{ values.name }}"
    backstage.io/kubernetes-namespace: "${{ values.namespace }}"
    argocd/app-name: "${{ values.name }}"
    postgres.version: "${{ values.pgVersion }}"
    environment: "${{ values.env }}"
  tags:
    - ferretdb
    - mongodb
    - postgres
    - stackgres
    - kubernetes
    - argocd
    - database
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/ferretdb/${{ values.name }}"
      title: "FerretDB Config Repository"
    - url: "https://ferretdb-monitoring.example.com/${{ values.name }}"
      title: "FerretDB Monitoring Dashboard"
spec:
  type: ferretdb
  owner: default/eset
  lifecycle: experimental
  system: ferretdb
  environment: "${{ values.env }}"
  dependsOn:
    - component:${{ values.env }}/${{ values.name }}-postgres
