apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${{ values.name }}
  namespace: ${{ values.namespace }}
  labels:
    app: ${{ values.name }}
    backstage.io/kubernetes-id: ${{ values.name }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
spec:
  replicas: ${{ values.replicas }}
  selector:
    matchLabels:
      app: ${{ values.name }}
  template:
    metadata:
      labels:
        app: ${{ values.name }}
        backstage.io/kubernetes-id: ${{ values.name }}
        backstage.io/kubernetes-namespace: ${{ values.namespace }}
    spec:
      containers:
        - name: ferretdb
          image: ${{ values.image }}
          ports:
            - containerPort: 27017
              name: mongodb
          env:
            - name: FERRETDB_POSTGRESQL_URL
              value: postgres://${{ values.postgresName }}/ferretdb
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
