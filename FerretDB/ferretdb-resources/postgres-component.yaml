apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: "${{ values.name }}"
  title: "${{ values.name }}"
  namespace: "${{ values.env }}"
  annotations:
    backstage.io/kubernetes-id: "${{ values.name }}"
    backstage.io/kubernetes-namespace: "${{ values.namespace }}"
    argocd/app-name: "${{ values.ferretdbName }}"
    postgres.version: "${{ values.pgVersion }}"
    environment: "${{ values.env }}"
  tags:
    - postgres
    - stackgres
    - ferretdb-dependency
    - kubernetes
    - argocd
    - database
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/postgres/${{ values.name }}"
      title: "PostgreSQL Config Repository (for FerretDB)"
    - url: "https://postgres-monitoring.example.com/${{ values.name }}"
      title: "PostgreSQL Monitoring Dashboard (for FerretDB)"
spec:
  type: postgres-cluster
  owner: default/eset
  lifecycle: experimental
  system: postgres
  environment: "${{ values.env }}"