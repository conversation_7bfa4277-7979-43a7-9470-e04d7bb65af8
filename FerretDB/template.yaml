apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: ferretdb-stackgres
  title: "FerretDB on StackGres"
  description: "Backstage Scaffolder template for deploying FerretDB on top of StackGres PostgreSQL cluster using GitLab and ArgoCD"
  tags:
    - ferretdb
    - stackgres
    - postgres
    - mongodb
    - argocd
    - gitlab
spec:
  owner: default/eset
  type: service

  parameters:
    - title: "Basic Configuration"
      required:
        - name
        - namespace
        - pgVersion
      properties:
        name:
          title: "FerretDB Name"
          type: string
          ui:placeholder: "ferretdb-app"
          ui:help: "Set a unique name for the FerretDB deployment."
          default: "ferretdb-app"
        namespace:
          title: "Namespace"
          type: string
          ui:placeholder: "ferretdb"
          ui:help: "Namespace in which FerretDB and PostgreSQL resources will be created."
          default: "ferretdb"
        pgVersion:
          title: "PostgreSQL Version"
          type: string
          ui:placeholder: "15"
          ui:help: "The major version of PostgreSQL to use for the persistence cluster."
          default: "15"
          enum:
            - "13"
            - "14"
            - "15"

    - title: "Cluster Configuration"
      properties:
        replicas:
          title: "PostgreSQL Replicas"
          type: number
          ui:placeholder: 1
          ui:help: "Total number of PostgreSQL instances, including the primary."
          default: 1
          minimum: 1
        storageSize:
          title: "Storage Size"
          type: string
          ui:placeholder: "5Gi"
          ui:help: "Size of persistent storage for PostgreSQL data (e.g. 5Gi, 10Gi)."
          default: "5Gi"
        serviceType:
          title: "Service Type"
          type: string
          ui:placeholder: "ClusterIP"
          ui:help: "Select the Kubernetes Service type for PostgreSQL primary."
          enum:
            - "ClusterIP"
            - "NodePort"
            - "LoadBalancer"
          default: "ClusterIP"

    - title: "PostgreSQL Configuration"
      properties:
        sharedBuffers:
          title: "shared_buffers"
          type: string
          ui:placeholder: "128MB"
          ui:help: "Amount of memory reserved for PostgreSQL shared_buffers."
          default: "128MB"
        maxConnections:
          title: "max_connections"
          type: number
          ui:placeholder: 100
          ui:help: "Maximum number of concurrent PostgreSQL database connections."
          default: 100

    - title: "FerretDB Configuration"
      properties:
        ferretdbReplicas:
          title: "FerretDB Replicas"
          type: number
          ui:placeholder: 1
          ui:help: "How many FerretDB pods to run for high availability."
          default: 1
          minimum: 1
        ferretdbImage:
          title: "FerretDB Image"
          type: string
          ui:placeholder: "ghcr.io/ferretdb/ferretdb:latest"
          ui:help: "Container image reference for FerretDB."
          default: "ghcr.io/ferretdb/ferretdb:latest"
        ferretdbPassword:
          title: "FerretDB Database Password"
          type: string
          ui:placeholder: "secure_password"
          ui:help: "Password for the FerretDB database user. Will be stored as a Kubernetes Secret."
          default: "ferretdb_password"
          ui:field: Secret

    - title: "Repository Configuration"
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
      properties:
        gitlabUrl:
          title: "GitLab URL"
          type: string
          ui:placeholder: "gitlab.cluster.eset.corp"
          ui:help: "Select your GitLab instance to store application repositories."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: "Environment"
          type: string
          ui:placeholder: "deva"
          ui:help: "Specify environment where FerretDB will be deployed (deva/prod)."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: "Project Name"
          type: string
          ui:placeholder: "sigproject"
          ui:help: "GitLab project or group name owning these repositories."
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage application specifications."
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository containing ArgoCD application manifests."
          default: "backstage-argocd-apps"

  steps:
    # 1. Generate namespace manifest
    - id: generate-namespace
      name: "Generate namespace manifest"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/namespace.yaml
        targetPath: ./output/namespace.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

    # 2. Generate SGPoolingConfig manifest
    - id: generate-sgpoolingconfig
      name: "Generate SGPoolingConfig manifest"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/sgpoolingconfig.yaml
        targetPath: ./output/sgpoolingconfig.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

    # 3. Generate SGScript for user and database creation
    - id: generate-sgscript
      name: "Generate SGScript for user and database creation"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/sgscript.yaml
        targetPath: ./output/sgscript.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}
          password: ${{ parameters.ferretdbPassword }}

    # 4. Generate SGInstanceProfile manifest
    - id: generate-sginstanceprofile
      name: "Generate SGInstanceProfile manifest"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/sginstanceprofile.yaml
        targetPath: ./output/sginstanceprofile.yaml
        values:
          name: ${{ parameters.name | lower }}-postgres
          namespace: ${{ parameters.namespace | lower }}

    # 5. Generate SGCluster manifest
    - id: generate-sgcluster
      name: "Generate SGCluster manifest"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/sgcluster.yaml
        targetPath: ./output/sgcluster.yaml
        values:
          name: ${{ parameters.name | lower }}-postgres
          namespace: ${{ parameters.namespace | lower }}
          pgVersion: ${{ parameters.pgVersion }}
          sharedBuffers: ${{ parameters.sharedBuffers }}
          maxConnections: ${{ parameters.maxConnections | int }}
          replicas: ${{ parameters.replicas }}
          serviceType: ${{ parameters.serviceType }}
          storageSize: ${{ parameters.storageSize }}

    # 6. Generate FerretDB deployment
    - id: generate-ferretdb-deployment
      name: "Generate FerretDB deployment"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/ferretdb-deployment.yaml
        targetPath: ./output/ferretdb-deployment.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          replicas: ${{ parameters.ferretdbReplicas }}
          image: ${{ parameters.ferretdbImage }}
          postgresName: ${{ parameters.name | lower }}-postgres

    # 7. Generate FerretDB service
    - id: generate-ferretdb-service
      name: "Generate FerretDB service"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/ferretdb-service.yaml
        targetPath: ./output/ferretdb-service.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}

    # 8. Create FerretDB Component
    - id: create-ferretdb-component
      name: "Create FerretDB Component"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/ferretdb-component.yaml
        targetPath: ./output/ferretdb-component.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}
          pgVersion: ${{ parameters.pgVersion }}

    # 9. Create PostgreSQL Component
    - id: create-postgres-component
      name: "Create PostgreSQL Component"
      action: fetch:template:file
      input:
        url: ./ferretdb-resources/postgres-component.yaml
        targetPath: ./output/postgres-component.yaml
        values:
          name: ${{ parameters.name | lower }}-postgres
          ferretdbName: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}
          pgVersion: ${{ parameters.pgVersion }}

    # 10. Create commit for FerretDB and PostgreSQL config to GitLab
    - id: create-commit-for-ferretdb-config
      name: "Create Commit for FerretDB & PostgreSQL Config to GitLab"
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/ferretdb/${{ parameters.name | lower }}
        commitMessage: "Add configuration for FerretDB '${{ parameters.name | lower }}' and its PostgreSQL dependency"

    # 11. Register the FerretDB component in the catalog
    - id: register-ferretdb-component
      name: "Register FerretDB Component in Catalog"
      action: catalog:register
      input:
        catalogInfoUrl: ${{ ("https://" + parameters.gitlabUrl + "/" + parameters.projectName + "/" + parameters.appsRepoName + "/-/blob/main/" + parameters.env + "/ferretdb/" + parameters.name | lower + "/ferretdb-component.yaml") | string }}

    # 12. Register the PostgreSQL component in the catalog
    - id: register-postgres-component
      name: "Register PostgreSQL Component in Catalog"
      action: catalog:register
      input:
        catalogInfoUrl: ${{ ("https://" + parameters.gitlabUrl + "/" + parameters.projectName + "/" + parameters.appsRepoName + "/-/blob/main/" + parameters.env + "/ferretdb/" + parameters.name | lower + "/postgres-component.yaml") | string }}

    # 13. Generate ArgoCD application configuration
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          ferretdbResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.projectName }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        targetPath: ./argocd-config

    # 14. Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.name | lower }}-ferretdb-application.yaml

    # 15. Create MR for ArgoCD config to GitLab
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/${{ parameters.name | lower }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: ${{ parameters.env }}/ferretdb
        description: "Add ArgoCD configuration for FerretDB '${{ parameters.name | lower }}'"
        title: "Add FerretDB '${{ parameters.name | lower }}' configuration"

  output:
    links:
      - title: Open the FerretDB Resources Repository
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/ferretdb/${{ parameters.name | lower }}
      - title: Open the FerretDB Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-ferretdb-component'].output.entityRef }}
      - title: Open the PostgreSQL Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-postgres-component'].output.entityRef }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
    clusterName: ${{ parameters.name | lower }}
    namespace: ${{ parameters.namespace | lower }}
