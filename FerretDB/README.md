# FerretDB on StackGres Template

This template creates a FerretDB deployment on top of a StackGres PostgreSQL cluster in Kubernetes using GitLab for configuration storage and ArgoCD for deployment.

## What is FerretDB?

FerretDB is a fully open source MongoDB-compatible database, which uses PostgreSQL as a data storage backend. FerretDB is a stateless application that exposes a MongoDB wire-protocol TCP interface, allowing MongoDB clients to connect to a PostgreSQL database.

## Template Overview

The template provides configuration options for both the PostgreSQL cluster and the FerretDB deployment:

1. **Basic Configuration**
   - FerretDB name and namespace
   - PostgreSQL version

2. **Cluster Configuration**
   - Number of PostgreSQL replicas
   - Storage size
   - Service type

3. **PostgreSQL Configuration**
   - Basic PostgreSQL parameters (shared_buffers, max_connections)

4. **FerretDB Configuration**
   - Number of FerretDB replicas
   - FerretDB container image
   - Database password

5. **Repository Configuration**
   - GitLab URL and project details
   - Environment (deva, prod)
   - Repository names for apps and ArgoCD configurations

## Usage

1. Select the template in Backstage
2. Fill in the required parameters
3. Click "Create" to deploy FerretDB with a StackGres PostgreSQL backend

## Template Structure

The template follows a GitOps approach using GitLab and ArgoCD:

1. **Generate Resources**: Creates namespace, SGPoolingConfig, SGScript, SGCluster, and FerretDB deployment manifests
2. **Push to GitLab**: Pushes the configuration to a GitLab repository
3. **Register in Catalog**: Registers the FerretDB deployment as a component in the Backstage catalog
4. **Create ArgoCD Application**: Creates an ArgoCD application to deploy the resources

## Kubernetes Resources

The template creates the following Kubernetes resources:

- Namespace for FerretDB and PostgreSQL
- SGPoolingConfig customized for FerretDB
- SGScript to create the database user and database
- SGCluster for the PostgreSQL backend
- SGInstanceProfile for the PostgreSQL cluster
- Deployment for FerretDB
- Service for FerretDB
- Backstage component for FerretDB
- ArgoCD application for deployment

## Connecting to FerretDB

Once deployed, you can connect to FerretDB using any MongoDB client with the following connection string:

```
*******************************************************************************
```

Where:
- `PASSWORD` is the password specified in the template parameters
- `FERRETDB_SERVICE` is the name of the FerretDB service (usually `<name>.<namespace>.svc.cluster.local`)

## GitOps Workflow

The template follows a GitOps workflow:

1. Configuration is stored in GitLab
2. ArgoCD watches the GitLab repository for changes
3. ArgoCD automatically applies changes to the Kubernetes cluster
4. Backstage provides a unified view of the FerretDB deployments
