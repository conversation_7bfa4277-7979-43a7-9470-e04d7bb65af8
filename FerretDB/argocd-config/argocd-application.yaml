apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.name }}
  namespace: ec-argocd
spec:
  project: default
  source:
    repoURL: ${{ values.ferretdbResourcesRepoUrl }}
    targetRevision: HEAD
    path: ${{ values.env }}/ferretdb/${{ values.name }}
    directory:
      include: "{*.yml,*.yaml}"
      exclude: '*-component.yaml'
      recurse: true
  destination:
    name: idp-test
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated: {}
    syncOptions:
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
  ignoreDifferences:
  - group: stackgres.io
    kind: SGCluster
    name: ${{ values.name }}
    namespace: ${{ values.namespace }}
    jsonPointers:
    - /spec/managedSql/scripts
    - /spec/postgres/version
    - /spec/postgres/parameters
    - /spec/service
