apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.name }}-sharded-postgres
  namespace: ec-argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: ${{ values.shardedResourcesRepoUrl }}
    targetRevision: HEAD
    path: ${{ values.env }}/sharded-postgres/${{ values.name }}
    directory:
      include: "{*.yml,*.yaml}"
      exclude: '*-component.yaml'
      recurse: true
  destination:
    namespace: ${{ values.namespace }}
    name: idp-test
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
  ignoreDifferences:
  - group: stackgres.io
    kind: SGShardedCluster
    name: ${{ values.name }}
    namespace: ${{ values.namespace }}
    jsonPointers:
    - /spec/managedSql/scripts
    - /spec/postgres/version
    - /spec/postgres/parameters
    - /spec/service

