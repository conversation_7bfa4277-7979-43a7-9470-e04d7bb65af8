apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: sharded-stackgres
  title: "StackGres Sharded Cluster Creation"
  description: "Backstage Scaffolder template for deploying a StackGres PostgreSQL sharded cluster using GitLab and ArgoCD"
  tags:
    - stackgres
    - postgres
    - sharded
    - citus
    - argocd
    - gitlab
spec:
  owner: default/eset
  type: service

  parameters:
    - title: "Basic Configuration"
      required:
        - name
        - namespace
        - pgVersion
        - database
      properties:
        name:
          title: "Sharded Cluster Name"
          type: string
          ui:placeholder: "my-sharded-cluster"
          ui:help: "Provide a unique name for your StackGres Sharded Cluster resource (SGShardedCluster)."
          default: "sharded-cluster"
        namespace:
          title: "Namespace"
          type: string
          ui:placeholder: "sharded-cluster"
          ui:help: "Specify the Kubernetes namespace where this sharded cluster will be created."
          default: "sharded-cluster"
        database:
          title: "Database Name"
          type: string
          ui:placeholder: "mydatabase"
          ui:help: "The database name that will be created and used across all nodes and where distributed tables will live."
          default: "mydatabase"
        pgVersion:
          title: "PostgreSQL Version"
          type: string
          ui:placeholder: "15"
          ui:help: "Select the major version of PostgreSQL to deploy."
          default: "15"
          enum:
            - "13"
            - "14"
            - "15"

    - title: "Coordinator Configuration"
      properties:
        coordinatorReplicas:
          title: "Coordinator Replicas"
          type: number
          minimum: 1
          ui:placeholder: 2
          ui:help: "Set the total number of PostgreSQL instances for the coordinator, including the primary."
          default: 2
        coordinatorStorageSize:
          title: "Coordinator Storage Size"
          type: string
          ui:placeholder: "10Gi"
          ui:help: "Size of the Persistent Volume Claim for coordinator PostgreSQL data (e.g., 10Gi, 20Gi)."
          default: "10Gi"
        coordinatorServiceType:
          title: "Coordinator Service Type"
          type: string
          ui:placeholder: "ClusterIP"
          ui:help: "Choose the Kubernetes Service type to expose the coordinator primary instance."
          enum:
            - "ClusterIP"
            - "NodePort"
            - "LoadBalancer"
          default: "ClusterIP"

    - title: "Shards Configuration"
      properties:
        shardClusters:
          title: "Number of Shard Clusters"
          type: number
          minimum: 1
          ui:placeholder: 3
          ui:help: "Number of shard clusters to create."
          default: 3
        instancesPerCluster:
          title: "Instances Per Shard Cluster"
          type: number
          minimum: 1
          ui:placeholder: 2
          ui:help: "Number of PostgreSQL instances per shard cluster."
          default: 2
        shardStorageSize:
          title: "Shard Storage Size"
          type: string
          ui:placeholder: "10Gi"
          ui:help: "Size of the Persistent Volume Claim for shard PostgreSQL data (e.g., 10Gi, 20Gi)."
          default: "10Gi"
        shardServiceType:
          title: "Shard Service Type"
          type: string
          ui:placeholder: "ClusterIP"
          ui:help: "Choose the Kubernetes Service type to expose the shard primary instances."
          enum:
            - "ClusterIP"
            - "NodePort"
            - "LoadBalancer"
          default: "ClusterIP"

    - title: "PostgreSQL Configuration"
      properties:
        sharedBuffers:
          title: "shared_buffers"
          type: string
          ui:placeholder: "128MB"
          ui:help: "Amount of memory allocated for PostgreSQL shared_buffers parameter."
          default: "128MB"
        maxConnections:
          title: "max_connections"
          type: number
          ui:placeholder: 100
          ui:help: "Maximum number of concurrent PostgreSQL connections allowed."
          default: 100
        password:
          title: "Database User Password"
          type: string
          ui:placeholder: "secure_password"
          ui:help: "Password for the database user. Will be used in the SGScript."
          default: "admin123"
          ui:field: Secret

    - title: "Repository Configuration"
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
      properties:
        gitlabUrl:
          title: "GitLab URL"
          type: string
          ui:placeholder: "gitlab.cluster.eset.corp"
          ui:help: "Select your GitLab instance used to store configuration repositories."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: "Environment"
          type: string
          ui:placeholder: "deva"
          ui:help: "Target environment for this cluster deployment: deva (development) or prod (production)."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: "Project Name"
          type: string
          ui:placeholder: "sigproject"
          ui:help: "Enter the name of the GitLab project (repository owner or group)."
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage app component definitions."
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository name used for ArgoCD application manifests."
          default: "backstage-argocd-apps"

  steps:
    # 1. Generate namespace manifest
    - id: generate-namespace
      name: "Generate namespace manifest"
      action: fetch:template:file
      input:
        url: ./sharded-resources/namespace.yaml
        targetPath: ./output/namespace.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

    # 2. Generate SGScript for user and database creation
    - id: generate-sgscript
      name: "Generate SGScript for user and database creation"
      action: fetch:template:file
      input:
        url: ./sharded-resources/sgscript.yaml
        targetPath: ./output/sgscript.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}
          password: ${{ parameters.password }}
          database: ${{ parameters.database }}

    # 3. Generate SGInstanceProfile for coordinator
    - id: generate-coordinator-sginstanceprofile
      name: "Generate SGInstanceProfile for coordinator"
      action: fetch:template:file
      input:
        url: ./sharded-resources/coordinator-sginstanceprofile.yaml
        targetPath: ./output/coordinator-sginstanceprofile.yaml
        values:
          name: ${{ parameters.name | lower }}-coordinator
          namespace: ${{ parameters.namespace | lower }}

    # 4. Generate SGInstanceProfile for shards
    - id: generate-shards-sginstanceprofile
      name: "Generate SGInstanceProfile for shards"
      action: fetch:template:file
      input:
        url: ./sharded-resources/shards-sginstanceprofile.yaml
        targetPath: ./output/shards-sginstanceprofile.yaml
        values:
          name: ${{ parameters.name | lower }}-shards
          namespace: ${{ parameters.namespace | lower }}

    # 5. Generate SGShardedCluster manifest
    - id: generate-sgshardedcluster
      name: "Generate SGShardedCluster manifest"
      action: fetch:template:file
      input:
        url: ./sharded-resources/sgshardedcluster.yaml
        targetPath: ./output/sgshardedcluster.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          database: ${{ parameters.database }}
          pgVersion: ${{ parameters.pgVersion }}
          sharedBuffers: ${{ parameters.sharedBuffers }}
          maxConnections: ${{ parameters.maxConnections }}
          coordinatorReplicas: ${{ parameters.coordinatorReplicas }}
          coordinatorStorageSize: ${{ parameters.coordinatorStorageSize }}
          coordinatorServiceType: ${{ parameters.coordinatorServiceType }}
          shardClusters: ${{ parameters.shardClusters }}
          instancesPerCluster: ${{ parameters.instancesPerCluster }}
          shardStorageSize: ${{ parameters.shardStorageSize }}
          shardServiceType: ${{ parameters.shardServiceType }}

    # 6. Create Sharded Cluster Component
    - id: create-sharded-component
      name: "Create Sharded Cluster Component"
      action: fetch:template:file
      input:
        url: ./sharded-resources/sharded-component.yaml
        targetPath: ./output/sharded-component.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          pgVersion: ${{ parameters.pgVersion }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    # 7. Create commit for Sharded Cluster config to GitLab
    - id: create-commit-for-sharded-config
      name: "Create Commit for Sharded Cluster Config to GitLab"
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/sharded-postgres/${{ parameters.name | lower }}
        commitMessage: "Add configuration for Sharded PostgreSQL cluster '${{ parameters.name | lower }}'"

    # 8. Register the component in the catalog
    - id: register-sharded-component
      name: "Register Sharded Cluster Component in Catalog"
      action: catalog:register
      input:
        catalogInfoUrl: ${{ ("https://" + parameters.gitlabUrl + "/" + parameters.projectName + "/" + parameters.appsRepoName + "/-/blob/main/" + parameters.env + "/sharded-postgres/" + parameters.name | lower + "/sharded-component.yaml") | string }}

    # 9. Generate ArgoCD application configuration
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          shardedResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.projectName }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
          destinationId: idp-test
        targetPath: ./argocd-config

    # 10. Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.name | lower }}-sharded-application.yaml

    # 11. Create MR for ArgoCD config to GitLab
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/${{ parameters.name | lower }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: ${{ parameters.env }}/sharded-postgres
        description: "Add ArgoCD configuration for Sharded PostgreSQL cluster '${{ parameters.name | lower }}'"
        title: "Add Sharded PostgreSQL cluster '${{ parameters.name | lower }}' configuration"

  output:
    links:
      - title: Open the Sharded PostgreSQL Resources Repository
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/sharded-postgres/${{ parameters.name | lower }}
      - title: Open the Sharded PostgreSQL Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-sharded-component'].output.entityRef }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
    clusterName: ${{ parameters.name | lower }}
    namespace: ${{ parameters.namespace | lower }}
