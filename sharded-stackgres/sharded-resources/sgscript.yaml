apiVersion: stackgres.io/v1
kind: SGScript
metadata:
  name: createuserdb
  namespace: ${{ values.namespace }}
spec:
  scripts:
  - name: create-user
    script: |
      create user ${{ values.database }}_user with password '${{ values.password }}';
  - name: create-database
    script: |
      create database ${{ values.database }} owner ${{ values.database }}_user encoding 'UTF8' locale 'en_US.UTF-8' template template0;
  - name: distribute-tables
    database: ${{ values.database }}
    user: ${{ values.database }}_user
    script: |
      -- This script will be executed after the database is created
      -- Add any Citus-specific initialization here
      -- Example: SELECT create_distributed_table('table_name', 'distribution_column');
