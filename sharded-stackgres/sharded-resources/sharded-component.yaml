apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.name }}
  namespace: "${{ values.env }}"
  annotations:
    backstage.io/kubernetes-id: ${{ values.name }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
    backstage.io/techdocs-ref: dir:.
    gitlab.com/project-slug: ${{ values.projectName }}/${{ values.appsRepoName }}
  links:
    - url: https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/sharded-postgres/${{ values.name }}
      title: Repository
      icon: gitlab
  tags:
    - stackgres
    - postgres
    - sharded
    - citus
    - ${{ values.env }}
spec:
  type: service
  lifecycle: production
  owner: default/eset
  system: postgres
  dependsOn: []
  providesApis: []
  consumesApis: []
  description: |
    StackGres Sharded PostgreSQL Cluster (${{ values.pgVersion }}) with Citus extension.
    
    This is a sharded PostgreSQL cluster with ${{ values.coordinatorReplicas }} coordinator instances and 
    ${{ values.shardClusters }} shard clusters with ${{ values.instancesPerCluster }} instances each.
    
    Database: ${{ values.database }}
    Namespace: ${{ values.namespace }}
