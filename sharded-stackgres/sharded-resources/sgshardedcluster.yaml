apiVersion: stackgres.io/v1alpha1
kind: SGShardedCluster
metadata:
  name: ${{ values.name }}
  namespace: ${{ values.namespace }}
  labels:
    backstage.io/kubernetes-id: ${{ values.name }}
spec:
  type: citus
  database: ${{ values.database }}
  postgres:
    version: '${{ values.pgVersion }}'
    parameters:
      shared_buffers: "${{ values.sharedBuffers }}"
      max_connections: ${{ values.maxConnections | int }}
  
  coordinator:
    instances: ${{ values.coordinatorReplicas }}
    sgInstanceProfile: '${{ values.name }}-coordinator'
    pods:
      persistentVolume:
        size: '${{ values.coordinatorStorageSize }}'
    managedSql:
      scripts:
      - sgScript: createuserdb
  
  shards:
    clusters: ${{ values.shardClusters }}
    instancesPerCluster: ${{ values.instancesPerCluster }}
    sgInstanceProfile: '${{ values.name }}-shards'
    pods:
      persistentVolume:
        size: '${{ values.shardStorageSize }}'
  
  prometheusAutobind: true
