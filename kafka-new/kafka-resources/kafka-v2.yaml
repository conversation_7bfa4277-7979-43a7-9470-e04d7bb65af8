apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: metrics
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  partitions: 1
  replicas: 1
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: logs
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  partitions: 1
  replicas: 1
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: bastion
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  partitions: 1
  replicas: 1
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: cwpp-dev-k8s-stdout
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  partitions: 1
  replicas: 1
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: dps-cwpp-prometheus-metrics
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  partitions: 1
  replicas: 1

---

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: kafka-logs
  name: kafka-super-user
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  authentication:
    type: tls
  authorization:
    acls:
      - operation: All
        resource:
          name: '*'
          patternType: literal
          type: topic
      - operation: All
        resource:
          name: '*'
          patternType: literal
          type: group
    type: simple
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: kafka-logs
  name: kafka-metrics-user
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      - resource:
          type: topic
          name: metrics
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: kafka-logs
  name: kafka-logs-user
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      - resource:
          type: topic
          name: logs
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: kafka-logs
  name: kafka-logs-bastion
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      - resource:
          type: topic
          name: bastion
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: kafka-logs
  name: kafka-logs-consumer
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      - resource:
          type: topic
          name: bastion
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: topic
          name: logs
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: topic
          name: metrics
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: topic
          name: dps-cwpp-prometheus-metrics
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: topic
          name: cwpp-dev-k8s-stdout
          patternType: literal
        operation: All
        host: "*"
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"


---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: dual-role
  namespace: kafka-logs
  labels:
    strimzi.io/cluster: kafka-logs
spec:
  replicas: 3
  roles:
    - controller
    - broker
  storage:
    type: jbod
    volumes:
      - id: 0
        type: persistent-claim
        size: 10Gi
        deleteClaim: false
        kraftMetadata: shared
--- 
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: kafka-logs
  namespace: kafka-logs
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 3.8.0
    logging:
      type: inline
      loggers:
        rootLogger.level: "INFO"
    listeners:
      - name: internal
        port: 9292
        type: internal
        tls: true
        authentication:
          type: tls
      - name: nodeport
        port: 9094
        type: nodeport
        tls: true
        authentication:
          type: tls
        configuration:
          bootstrap:
            nodePort: 32100
          brokers:
          - broker: 0
            nodePort: 32000
          - broker: 1
            nodePort: 32001
          - broker: 2
            nodePort: 32002
      - name: external
        port: 9294
        type: ingress
        tls: true
        authentication:
          type: tls
        configuration:
          bootstrap:
            host: "bootstrap.cwpp.deva.esc.esetrs.cz"
            annotations:
              external-dns.alpha.kubernetes.io/hostname: bootstrap.cwpp.deva.esc.esetrs.cz
                #external-dns.alpha.kubernetes.io/ttl: "60"
              kubernetes.io/ingress.class: ec-platform
          brokers:
            - broker: 0
              host: "broker0.cwpp.deva.esc.esetrs.cz"
                #advertisedPort: 9294
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker0.cwpp.deva.esc.esetrs.cz
                  #external-dns.alpha.kubernetes.io/ttl: "60"
                kubernetes.io/ingress.class: ec-platform
            - broker: 1
              host: "broker1.cwpp.deva.esc.esetrs.cz"
                #advertisedPort: 9294
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker1.cwpp.deva.esc.esetrs.cz
                  #external-dns.alpha.kubernetes.io/ttl: "60"
                kubernetes.io/ingress.class: ec-platform
            - broker: 2
              host: "broker2.cwpp.deva.esc.esetrs.cz"
                #advertisedPort: 9294
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker2.cwpp.deva.esc.esetrs.cz
                  #external-dns.alpha.kubernetes.io/ttl: "60"
                kubernetes.io/ingress.class: ec-platform
    authorization:
      # Enable authorization using Kafka's simple ACL authorizer
      type: simple
      # NOTES:
      # If the Kafka cluster has no authorization mechanism configured (authorizer.class.name is not set),
      # then there are no restrictions on what any user can do. In this case, all users, including those defined via KafkaUser resources,
      # will have unlimited access to all Kafka resources. This means they can produce to and consume from all topics, create and delete topics, etc.
      # In Kafka, if authorization is enabled but no specific ACLs are set for a user, the default behavior is to deny access.

    config:
      # Enable authorization using Kafka's simple ACL authorizer
      # authorizer.class.name: kafka.security.authorizer.AclAuthorizer
      log.retention.hours: 1
      log.retention.bytes: 1007374182 # ~1000MB
        # ssl.endpoint.identification.algorithm: ""
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
      default.replication.factor: 3
      min.insync.replicas: 2
  entityOperator:
    topicOperator: {}
    userOperator: {}
