# kafka-topics.yaml template:
{%- if values.topics %}
{%- for topic in values.topics %}
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: ${{ topic.name }}
  namespace: ${{ values.namespace }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  partitions: ${{ topic.partitions }}
  replicas: ${{ topic.replicas }}
---
{%- endfor %}
{%- endif %}

---
# kafka-users.yaml template:
{%- if values.users %}
{%- for user in values.users %}
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: ${{ values.namespace }}
  name: ${{ user.name }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  authentication:
    type: ${{ user.type }}
  authorization:
    type: simple
    acls:
    {%- for topic in user.topics %}
      - resource:
          type: topic
          name: ${{ topic }}
          patternType: literal
        operation: All
        host: "*"
    {%- endfor %}
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"
---
{%- endfor %}
{%- endif %}

---
# kafka-cluster.yaml template:
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: dual-role
  namespace: ${{ values.namespace }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  replicas: 3
  roles:
    - controller
    - broker
  storage:
    type: jbod
    volumes:
      - id: 0
        type: persistent-claim
        size: 10Gi
        deleteClaim: false
        kraftMetadata: shared

---
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: ${{ values.kafkaClusterName }}
  namespace: ${{ values.namespace }}
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 3.8.0
    logging:
      type: inline
      loggers:
        rootLogger.level: "INFO"
    listeners:
      - name: internal
        port: 9292
        type: internal
        tls: true
        authentication:
          type: tls
      - name: nodeport
        port: 9094
        type: nodeport
        tls: true
        authentication:
          type: tls
        configuration:
          bootstrap:
            nodePort: 32100
          brokers:
          - broker: 0
            nodePort: 32000
          - broker: 1
            nodePort: 32001
          - broker: 2
            nodePort: 32002
      - name: external
        port: 9294
        type: ingress
        tls: true
        authentication:
          type: tls
        configuration:
          bootstrap:
            host: "bootstrap.${{ values.domainSuffix }}"
            annotations:
              external-dns.alpha.kubernetes.io/hostname: bootstrap.${{ values.domainSuffix }}
              kubernetes.io/ingress.class: ec-platform
          brokers:
            - broker: 0
              host: "broker0.${{ values.domainSuffix }}"
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker0.${{ values.domainSuffix }}
                kubernetes.io/ingress.class: ec-platform
            - broker: 1
              host: "broker1.${{ values.domainSuffix }}"
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker1.${{ values.domainSuffix }}
                kubernetes.io/ingress.class: ec-platform
            - broker: 2
              host: "broker2.${{ values.domainSuffix }}"
              annotations:
                external-dns.alpha.kubernetes.io/hostname: broker2.${{ values.domainSuffix }}
                kubernetes.io/ingress.class: ec-platform
    authorization:
      type: simple
    config:
      log.retention.hours: 1
      log.retention.bytes: 1007374182
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
      default.replication.factor: 3
      min.insync.replicas: 2
  entityOperator:
    topicOperator: {}
    userOperator: {}
