apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: kafka-new-instance-template
  title: Create a Kafka Instance using Strimzi
  description: This template creates a Kafka instance using the Strimzi operator with topics and users.
  tags:
    - kafka
    - strimzi
    - kubernetes
    - argocd
spec:
  owner: your-team
  type: service

  parameters:
    # Page 1: Basic Configuration
    - title: Basic Configuration
      required:
        - namespace
        - kafkaClusterName
        - domainSuffix
      properties:
        namespace:
          title: Namespace
          type: string
          description: Kubernetes namespace where Kafka should be deployed
          default: kafka-logs
        kafkaClusterName:
          title: Kafka Cluster Name
          type: string
          description: The name of your Kafka cluster
          default: kafka-logs
        domainSuffix:
          title: Domain Suffix
          type: string
          description: Domain suffix for Kafka brokers (e.g. cwpp.deva.esc.esetrs.cz)
          default: cwpp.deva.esc.esetrs.cz
        kafkaVersion:
          title: Kafka Version
          type: string
          description: The version of Kafka to deploy
          default: "3.8.0"

    # Page 2: Storage Configuration
    - title: Storage Configuration
      properties:
        storage:
          title: Storage Configuration
          type: object
          properties:
            size:
              title: Storage Size
              type: string
              description: Size of the persistent volume
              default: "10Gi"
            deleteClaim:
              title: Delete PVC on cluster deletion
              type: boolean
              description: Whether to delete PVC when cluster is deleted
              default: false

    # Page 3: Topics Configuration
    - title: Kafka Topics
      properties:
        topics:
          title: Kafka Topics
          type: array
          description: List of Kafka topics to create
          items:
            type: object
            required:
              - name
              - partitions
              - replicas
            properties:
              name:
                type: string
                description: Topic name
              partitions:
                type: integer
                default: 1
                description: Number of partitions for the topic
              replicas:
                type: integer
                default: 1
                description: Number of replicas for the topic

    # Page 4: Users Configuration
    - title: Kafka Users
      properties:
        users:
          title: Kafka Users
          type: array
          description: List of Kafka users to create
          items:
            type: object
            required:
              - name
              - type
              - topics
            properties:
              name:
                type: string
                description: Username for Kafka authentication
              type:
                type: string
                enum: [tls]
                default: tls
                description: Authentication type for the user
              topics:
                type: array
                items:
                  type: string
                description: List of topics this user can access

    # Page 5: Network Configuration
    - title: Network Configuration
      properties:
        listeners:
          title: Kafka Listeners
          type: object
          properties:
            internal:
              type: object
              properties:
                port:
                  type: integer
                  default: 9292
            nodeport:
              type: object
              properties:
                enabled:
                  type: boolean
                  default: true
                bootstrapNodePort:
                  type: integer
                  default: 32100
            ingress:
              type: object
              properties:
                enabled:
                  type: boolean
                  default: true
                ingressClass:
                  type: string
                  default: ec-platform

  steps:
    - id: generate-kafka-resources
      name: Generating Kafka Resource Manifests
      action: fetch:template
      input:
        url: ./kafka-resources
        values:
          namespace: ${{ parameters.namespace }}
          kafkaClusterName: ${{ parameters.kafkaClusterName }}
          domainSuffix: ${{ parameters.domainSuffix }}
          kafkaVersion: ${{ parameters.kafkaVersion }}
          storage: ${{ parameters.storage }}
          topics: ${{ parameters.topics }}
          users: ${{ parameters.users }}
          listeners: ${{ parameters.listeners }}

    - id: create-commit-for-kafka-config
      name: Create Commit for Kafka Config to GitLab
      action: gitlab:repo:push
      input:
        repoUrl: gitlab.cluster.eset.corp?repo=backstage-apps&owner=sigproject
        branchName: main
        targetPath: kafka/${{ parameters.kafkaClusterName }}
        commitMessage: "Add configuration for Kafka cluster '${{ parameters.kafkaClusterName }}'"

    - id: register-kafka-component
      name: Registering the Kafka Component
      action: catalog:register
      input:
        repoContentsUrl: https://gitlab.cluster.eset.corp/sigproject/backstage-apps/-/blob/main/
        catalogInfoPath: /kafka/${{ parameters.kafkaClusterName }}/catalog-info.yaml

    - id: generate-argocd-config
      name: Generating Argo CD Application Configuration
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          kafkaClusterName: ${{ parameters.kafkaClusterName }}
          namespace: ${{ parameters.namespace }}
          kafkaResourcesRepoUrl: ${{ steps['create-commit-for-kafka-config'].output.remoteUrl }}
        targetPath: ./argocd-config

    - id: create-mr-for-argocd-config
      name: Create MR for Argo CD Config to GitLab
      action: publish:gitlab:merge-request
      input:
        repoUrl: gitlab.cluster.eset.corp?**********************apps&owner=sigproject
        branchName: feature/${{ parameters.kafkaClusterName }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: kafka
        description: "Add Argo CD configuration for Kafka cluster '${{ parameters.kafkaClusterName }}'"
        title: "Add Kafka cluster '${{ parameters.kafkaClusterName }}' configuration"

  output:
    links:
      - title: Open the Kafka Resources Repository
        url: https://gitlab.cluster.eset.corp/sigproject/backstage-apps/-/tree/main/kafka/${{ parameters.kafkaClusterName }}
      - title: Open the Kafka Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-kafka-component'].output.entityRef }}
      - title: View Merge Request for Argo CD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
