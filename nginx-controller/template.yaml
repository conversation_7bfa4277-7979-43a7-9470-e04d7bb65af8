apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: nginx-controller
  title: "Nginx Ingress Controller"
  description: >
    Backstage Scaffolder template for deploying an Nginx Ingress Controller via Helm,
    with GitOps managed config in GitLab and orchestrated by ArgoCD.
  tags:
    - nginx
    - ingress
    - kubernetes
    - argocd
    - gitlab
    - helm
    - ingress-nginx
spec:
  owner: default/eset
  type: service

  parameters:
    # Main configuration
    - title: "Nginx Controller Basic Configuration"
      required:
        - owner
        - name
        - namespace
        - ingressClass
        - replicaCount
        - scopeSelection
      properties:
        owner:
          title: "Owner"
          type: string
          description: "Owner of the component (group or user)"
          "ui:field": OwnerPicker
          default: "default/eset"
        name:
          title: "Nginx Deployment Name for nginx-controller"
          type: string
          pattern: "^(?!nginx-ingress$).*" # Disallow 'nginx-ingress'
          ui:placeholder: "my-team-nginx-controller"
          ui:help: "Choose a unique name for this Nginx controller deployment. The name 'nginx-ingress' is reserved."
        namespace:
          title: "Namespace"
          type: string
          description: "Kubernetes namespace for deployment nginx-controller"
          ui:placeholder: "my-team-namespace"
        ingressClass:
          title: "IngressClass"
          type: string
          pattern: "^(?!nginx$|ec-platform$).*" # Disallow 'nginx' or 'ec-platform'
          ui:placeholder: "my-custom-nginx-class"
          ui:help: "Specify the IngressClass resource name. Avoid using 'nginx' or 'ec-platform' as they might conflict with existing controllers."
        replicaCount:
          title: "Replica Count"
          type: number
          minimum: 1
          description: "Number of controller replicas"
          default: 2

        # Scope/affinity (alphabetical order for clarity)
        scopeSelection:
          title: "Limit Controller Scope By"
          type: string
          enum: ["namespace", "namespaceSelector"]
          default: "namespace"
        scopeNamespaceValue:
          title: "Namespace to watch (if 'namespace' is selected. Leave blank if selector is used.)"
          type: string
          default: ""
          ui:placeholder: "specific-app-namespace"
          ui:help: "If 'Limit Controller Scope By' is set to 'namespace', enter the single namespace this controller should manage."
        scopeNamespaceSelectorValue:
          title: "Namespace selector (if selector)"
          type: string
          ui:placeholder: "environment=production,team=my-team"
          ui:help: "If 'Limit Controller Scope By' is set to 'namespaceSelector', enter a Kubernetes label selector (e.g., 'key1=value1,key2=value2'). The controller will manage Ingresses in namespaces matching these labels."
        enableNodeAffinity:
          title: "Enable Node Affinity"
          type: boolean
          default: true
        nodeAffinityRole:
          title: "Node Role for Affinity"
          type: string
          enum: ["infrastructure-node", "worker-node"]
          default: "infrastructure-node"
        isAws:
          title: "Is AWS Environment?"
          type: boolean
          default: false
        enableHttpService:
          title: "Enable HTTP Service"
          type: boolean
          default: false
        nginxChartVersion:
          title: "Nginx Ingress Helm Chart Version"
          type: string
          default: "4.10.0"
          ui:help: "Specify the version of the ingress-nginx Helm chart to use. You can find available versions on Artifact Hub or the chart repository."

    # Repository & ArgoCD config (matches FerretDB grouping order)
    - title: "Repository & ArgoCD Configuration"
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
        - env
      properties:
        gitlabUrl:
          title: "GitLab URL"
          type: string
          description: "GitLab instance URL"
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: "Environment"
          type: string
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: "GitLab Project Name"
          type: string
          description: "GitLab project/group name (owner)"
          default: "sigproject"
          ui:placeholder: "my-gitlab-group/my-project"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          description: "Repository for app configs (values.yaml)"
          default: "backstage-apps"
          ui:placeholder: "my-apps-config-repo"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          description: "Repository for ArgoCD Application manifests"
          default: "backstage-argocd-apps"
          ui:placeholder: "my-argocd-manifests-repo"

  steps:
    # 1. Generate Helm values.yaml from template
    - id: generate-values
      name: "Generate Helm values.yaml"
      action: fetch:template:file
      input:
        url: ./nginx-controller-resources/values.yaml
        targetPath: ./output/helm-values/values.yaml
        values:
          isAws: ${{ parameters.isAws }}
          # Use the Handlebars 'eq' helper to ensure correct boolean evaluation
          isNamespaceScope: ${{ eq parameters.scopeSelection 'namespace' }}
          scopeNamespaceValue: ${{ parameters.scopeNamespaceValue }}
          scopeNamespaceSelectorValue: ${{ parameters.scopeNamespaceSelectorValue }}
          enableNodeAffinity: ${{ parameters.enableNodeAffinity }}
          nodeAffinityRole: ${{ parameters.nodeAffinityRole }}
          enableHttpService: ${{ parameters.enableHttpService }}
          replicaCount: ${{ parameters.replicaCount }}
          ingressClass: ${{ parameters.ingressClass }}
          fullnameOverrideValue: ${{ parameters.name | lower }}

    # 2. Generate Backstage component descriptor for Nginx
    - id: generate-nginx-component
      name: "Generate Nginx Backstage component"
      action: fetch:template:file
      input:
        url: ./nginx-controller-resources/nginx-component.yaml
        targetPath: ./output/nginx-component.yaml
        values:
          name: ${{ parameters.name }}
          namespace: ${{ parameters.namespace }}
          owner: ${{ parameters.owner }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    # 3. Commit Helm values/config/component to GitLab apps repo
    - id: create-commit-for-nginx-config
      name: "Create commit in GitLab apps repo"
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/nginx-controller/${{ parameters.name | lower }}
        commitMessage: "Add nginx-controller '${{ parameters.name | lower }}' config and component"

    # 4. Register Nginx component in the catalog
    - id: register-nginx-component
      name: "Register nginx component in Backstage catalog"
      action: catalog:register
      input:
        catalogInfoUrl: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/blob/main/${{ parameters.env }}/nginx-controller/${{ parameters.name | lower }}/nginx-component.yaml

    # 5. Generate ArgoCD Application manifest
    - id: generate-argocd-config
      name: "Generate ArgoCD Application manifest"
      action: fetch:template:file
      input:
        url: ./argocd-config/argocd-application.yaml
        targetPath: ./argocd-output/${{ parameters.name | lower }}-nginx-application.yaml
        values:
          name: ${{ parameters.name }}
          namespace: ${{ parameters.namespace }}
          gitlabUrl: ssh://*******************************:7999
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}
          nginxChartVersion: ${{ parameters.nginxChartVersion }}
          argocdApplicationNamespace: "ec-argocd"
          argocdApplicationProject: "default"

    # 6. Create MR for ArgoCD Application config
    - id: create-mr-for-argocd-config
      name: "Create Merge Request for ArgoCD config"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/nginx-${{ parameters.name | lower }}-setup-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-output
        targetPath: ${{ parameters.env }}/nginx-controller
        title: "feat: Add ArgoCD app for nginx-controller '${{ parameters.name | lower }}'"
        description: "Generated ArgoCD Application manifest for nginx-controller '${{ parameters.name | lower }}' in namespace '${{ parameters.namespace }}'."

  output:
    links:
      - title: "Open the Nginx resources repository"
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/nginx-controller/${{ parameters.name | lower }}
      - title: "Open the Nginx Component in Backstage"
        icon: catalog
        entityRef: ${{ steps['register-nginx-component'].output.entityRef }}
      - title: "View Merge Request for ArgoCD Config"
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
    clusterName: ${{ parameters.name | lower }}
    namespace: ${{ parameters.namespace | lower }}