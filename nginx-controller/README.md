# Nginx Ingress Controller Backstage Template

This Backstage Software Template enables rapid, GitOps-driven deployment of the [Nginx Ingress Controller](https://kubernetes.github.io/ingress-nginx/) to any supported Kubernetes cluster. All configuration is managed in GitLab, and deployment is orchestrated by ArgoCD using upstream Helm charts.

## Features

- Scaffolds a templated Nginx Ingress Controller deployment via Helm
- Generates Backstage catalog descriptor and project config
- Pushes configuration to a GitLab repository for full GitOps visibility/control
- Creates an ArgoCD multi-source application for automated deploy

## Template Parameters

| Parameter                | Description                                                                 | Default / Example           |
|--------------------------|-----------------------------------------------------------------------------|-----------------------------|
| owner                    | Backstage owner (group/user)                                                | (required)                  |
| name                     | Deployment name for the Nginx controller                                    | `nginx-ingress`             |
| namespace                | Kubernetes namespace for controller                                         | (required)                  |
| ingressClass             | Name of the IngressClass                                                    | `ec-platform`               |
| replicaCount             | Number of replicas                                                          | `2`                         |
| isAws                    | Whether to use AWS service annotations                                      | `false`                     |
| scopeSelection           | Limit controller scope by namespace or selector                             | `namespaceSelector`         |
| scopeNamespaceValue      | Namespace to watch (if scoped by namespace)                                 |                             |
| scopeNamespaceSelectorValue | Namespace selector (if using selector scope)                             | `meta.eset.com/tenant=platform` |
| enableNodeAffinity       | Enable scheduling affinity on specific node roles                           | `true`                      |
| nodeAffinityRole         | Node selector key                                                           | `infrastructure-node`       |
| enableHttpService        | Enable HTTP listener on service                                             | `false`                     |
| nginxChartVersion        | Helm chart version                                                          | `4.10.0`                    |
| gitlabUrl                | GitLab instance URL                                                         | `gitlab.cluster.eset.corp`  |
| projectName              | GitLab project/group name                                                   | `sigproject`                |
| appsRepoName             | Repository for Nginx configs (`values.yaml`)                                | `backstage-apps`            |
| argocdRepoName           | Repository for ArgoCD Application manifests                                 | `backstage-argocd-apps`     |
| env                      | Deployment environment                                                      | `deva`                      |

## GitOps Workflow

1. **Helm Configuration:** The template generates a `values.yaml` Helm config and a Backstage component YAML using your parameters.
2. **Config Push:** Configs are pushed automatically to your specified GitLab repository and environment directory.
3. **ArgoCD Application:** An ArgoCD App-of-Apps manifest is created, referencing both the values repo and the upstream Helm chart.
4. **Full Automation:** Once the merge request for the ArgoCD application is merged, ArgoCD will deploy and manage the Nginx Ingress Controller according to your settings.

## References

- [Official Nginx Ingress Controller Helm Chart](https://github.com/kubernetes/ingress-nginx)