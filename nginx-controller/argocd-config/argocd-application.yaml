apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: "${{ values.name | lower }}-nginx-application"
  namespace: "${{ values.argocdApplicationNamespace }}"
spec:
  project: "${{ values.argocdApplicationProject }}"
  destination:
    name: idp-test
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
    managedNamespaceMetadata:
      labels:
        meta.eset.com/tenant: platform
  sources:
    - repoURL: "${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}.git"
      targetRevision: HEAD
      ref: values
      directory:
        include: "{*.yml,*.yaml}"
        exclude: '*-component.yaml'
        recurse: true
    - repoURL: https://kubernetes.github.io/ingress-nginx
      chart: ingress-nginx
      targetRevision: "${{ values.nginxChartVersion }}"
      helm:
        valueFiles:
          - $values/${{ values.env }}/nginx-controller/${{ values.name | lower }}/helm-values/values.yaml
        releaseName: "${{ values.name | lower }}-nginx"
        ignoreMissingValueFiles: true