apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: "${{ values.name | lower }}"
  title: "${{ values.name | lower }}"
  namespace: "${{ values.env }}"
  description: "Nginx Ingress Controller - ${{ values.name | lower }} in ${{ values.namespace }} namespace"
  annotations:
    github.com/project-slug: "${{ values.projectName }}/${{ values.appsRepoName }}"
    backstage.io/kubernetes-id: "${{ values.name | lower }}"
    backstage.io/kubernetes-namespace: "${{ values.namespace | lower }}"
    gitlab.com/project-id: "${{ values.projectName }}/${{ values.appsRepoName }}"
    argocd/app-name: "${{ values.name | lower }}-nginx-application"
    environment: "${{ values.env }}"
  tags:
    - nginx
    - ingress
    - kubernetes
    - argocd
    - helm
    - "${{ values.env }}"
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/nginx-controller/${{ values.name | lower }}"
      title: "Nginx Controller Config Repository"
    - url: "https://kubernetes.github.io/ingress-nginx/"
      title: "Nginx Ingress Controller Documentation"
spec:
  type: nginx-controller
  lifecycle: "${{ values.env }}"
  owner: "${{ values.owner }}"
  system: "kubernetes-ingress"
  environment: "${{ values.env }}"
  