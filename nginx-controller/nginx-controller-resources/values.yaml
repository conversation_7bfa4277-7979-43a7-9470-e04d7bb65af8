# Templated values.yaml for ingress-nginx Helm chart
fullnameOverride: "${{ values.fullnameOverrideValue }}"
controller:
  labels:
    backstage.io/kubernetes-id: "${{ values.fullnameOverrideValue }}"
  ingressClass: "${{ values.ingressClass }}"
  ingressClassResource:
    controllerValue: k8s.io/${{ values.ingressClass }}
    name: "${{ values.ingressClass }}"
  watchIngressWithoutClass: false
  admissionWebhooks:
    enabled: false
  tolerations:
    - operator: "Exists"
  {% if values.affinity %}
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: kubernetes.io/role
                operator: In
                values:
                  - "${{ values.nodeAffinityRole }}"
  {% endif %}
  allowSnippetAnnotations: true
  scope:
    enabled: true
    {% if values.isNamespaceScope == true %}
    namespace: "${{ values.scopeNamespaceValue }}"
    {% endif %}
    {% if values.isNamespaceScope != true %}
    namespaceSelector: "${{ values.scopeNamespaceSelectorValue }}"
    {% endif %}
  service:
    enabled: true
    enableHttp: ${{ values.enableHttpService }}
    appProtocol: false
    annotations:
      {% if values.isAws == true %}
      service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
      {% endif %}
  replicaCount: ${{ values.replicaCount }}
  nodeSelector:
    kubernetes.io/os: linux
  metrics:
    enabled: true
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
  extraArgs:
    enable-ssl-passthrough: ""