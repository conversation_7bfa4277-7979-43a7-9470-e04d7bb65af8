Need to create backstage template for creating Kubernetes Gateway API resource in the fodler gatewayapi/gateway
You can inspire in @nginx-controller or @stack-gres as for 

The new template must be complied with current Backstage template standard.

In the @grpc-demo/grpc-test-v2/grpc-https-example.yaml, @grpc-demo/grpc-test-v3/grpc-echo.yaml and @grpc-demo/grpc-test-v4/grpc-basic-example.yaml  there are already implemented CRD for Gateway API.

The template must support Gateway for HTTP and HTTPS.

In the @grpc-demo/crds/gateway.networking.k8s.io_gateways.yaml there is a definition of Gateway API.

I need to support the most important fields from the Gateway API.

Need to support v1beta1 and optional v1, not sure, what is difference.



 
