# User template
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.userName }}
  namespace: ${{ values.kafkaClusterName }}
  title: ${{ values.userName }}
  annotations:
    argocd/app-name: ${{ values.kafkaClusterName }}
    kafka.cluster: ${{ values.kafkaClusterName }}
    environment: ${{ values.env }}
  tags:
    - kafka
    - user
    - authentication
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/kafka/${{ values.kafkaClusterName }}/users/resources/${{ values.userName }}.yaml"
      title: "User Configuration"
spec:
  type: kafka-user
  owner: default/eset
  lifecycle: experimental
  subcomponentOf: ${{ values.env }}/${{ values.kafkaClusterName }}
  system: ${{ values.env }}/kafka
  environment: ${{ values.env }}