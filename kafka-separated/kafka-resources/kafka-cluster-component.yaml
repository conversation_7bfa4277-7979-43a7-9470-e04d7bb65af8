apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.kafkaClusterName }}
  title: ${{ values.kafkaClusterName }}
  namespace: ${{ values.env }}
  annotations:
    backstage.io/kubernetes-id: ${{ values.kafkaClusterName }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
    argocd/app-name: ${{ values.kafkaClusterName }}
    kafka.domain-suffix: ${{ values.domainSuffix }}
    kafka.version: ${{ values.kafkaVersion }}
    environment: ${{ values.env }}
  tags:
    - kafka
    - strimzi
    - kubernetes
    - argocd
    - messaging
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/kafka/${{ values.kafkaClusterName }}"
      title: "Kafka Config Repository"
    - url: "https://kafka-monitoring.example.com/${{ values.kafkaClusterName }}"
      title: "Kafka Monitoring Dashboard"
spec:
  type: kafka-cluster
  owner: default/eset
  lifecycle: experimental
  system: kafka
  environment: ${{ values.env }}