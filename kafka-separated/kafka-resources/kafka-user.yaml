apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  namespace: ${{ values.namespace }}
  name: ${{ values.userName }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  authentication:
    type: ${{ values.type }}
  authorization:
    type: simple
    acls:
    {%- for topic in values.topics %}
      - resource:
          type: topic
          name: ${{ topic }}
          patternType: literal
        operation: All
        host: "*"
    {%- endfor %}
      - resource:
          type: group
          name: '*'
          patternType: literal
        operation: All
        host: "*"
