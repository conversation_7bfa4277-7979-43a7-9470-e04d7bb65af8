# Topic template
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.topicName }}
  namespace: ${{ values.kafkaClusterName }}
  title: ${{ values.topicName }}
  annotations:
    argocd/app-name: ${{ values.kafkaClusterName }}
    kafka.cluster: ${{ values.kafkaClusterName }}
    kafka.partitions: "${{ values.partitions }}"
    kafka.replicas: "${{ values.replicas }}"
    environment: ${{ values.env }}
  tags:
    - kafka
    - topic
    - messaging
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/kafka/${{ values.kafkaClusterName }}/topics/resources/${{ values.topicName }}.yaml"
      title: "Topic Configuration"
spec:
  type: kafka-topic
  owner: default/eset
  lifecycle: experimental
  system: ${{ values.env }}/kafka
  subcomponentOf: ${{ values.env }}/${{ values.kafkaClusterName }}
  environment: ${{ values.env }}