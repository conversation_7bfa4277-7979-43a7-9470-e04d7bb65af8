
---
# kafka-cluster.yaml template:
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: ${{ values.kafkaClusterName }}-node-pool
  namespace: ${{ values.namespace }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
    backstage.io/kubernetes-id: ${{ values.kafkaClusterName }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
spec:
  replicas: 3
  roles:
    - controller
    - broker
  storage:
    type: persistent-claim
    size: 10Gi
    deleteClaim: false
    class: "platform-ssd"


---
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: ${{ values.kafkaClusterName }}
  namespace: ${{ values.namespace }}
  labels:
    backstage.io/kubernetes-id: ${{ values.kafkaClusterName }}
    backstage.io/kubernetes-namespace: ${{ values.namespace }}
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 3.8.0
    logging:
      type: inline
      loggers:
        rootLogger.level: "INFO"
    listeners:
      - name: internal
        port: 9292
        type: internal
        tls: true
        authentication:
          type: tls
      - name: external
        port: 9294
        type: ingress
        tls: true
        authentication:
          type: tls
        configuration:
          class: ec-platform
          bootstrap:
            host: "bootstrap.${{ values.kafkaClusterName }}.${{ values.domainSuffix }}"
            annotations:
              cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
          brokers:
            - broker: 0
              host: "broker0.${{ values.kafkaClusterName }}.${{ values.domainSuffix }}"
              annotations:
                cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
            - broker: 1
              host: "broker1.${{ values.kafkaClusterName }}.${{ values.domainSuffix }}"
              annotations:
                cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
            - broker: 2
              host: "broker2.${{ values.kafkaClusterName }}.${{ values.domainSuffix }}"
              annotations:
                cert-manager.io/cluster-issuer: letsencrypt-issuer-prod
    authorization:
      type: simple
    config:
      log.retention.hours: 1
      log.retention.bytes: 1007374182
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
      default.replication.factor: 3
      min.insync.replicas: 2
  entityOperator:
    topicOperator: {}
    userOperator: {}
