apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.kafkaClusterName }}
  namespace: ec-argocd
spec:
  project: default
  source:
    repoURL: ${{ values.kafkaResourcesRepoUrl }}
    targetRevision: HEAD
    path: ${{ values.env }}/kafka/${{ values.kafkaClusterName }}
    directory:
      include: "{*.yml,*.yaml}"
      exclude: '*-component.yaml'
      recurse: true
  destination:
    name: idp-test
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated: {}
    syncOptions:
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
