apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: kafka-separated-instance-template
  title: Create a Kafka Instance using Strimzi
  description: This template creates a Kafka instance using the Strimzi operator with topics and users.
  tags:
    - kafka
    - strimzi
    - kubernetes
    - argocd
spec:
  owner: eset
  type: service

  parameters:
    # Page 1: Basic Configuration
    - title: Basic Configuration
      required:
        - namespace
        - kafkaClusterName
        - domainSuffix
      properties:
        kafkaClusterName:
          title: Kafka Cluster Name
          type: string
          ui:placeholder: "my-team-kafka"
          ui:help: "Provide a lowercase name for your Kafka cluster (letters, numbers, hyphens; must not start or end with hyphen)."
          pattern: '^[a-z0-9]+(-[a-z0-9]+)*$'
          errorMessage:
            pattern: "Kafka Cluster Name must be lowercase, include only a-z, 0-9, and hyphens, and must not start or end with a hyphen."
        namespace:
          title: Namespace
          type: string
          ui:placeholder: "my-team-namespace"
          ui:help: "Specify the Kubernetes namespace where the Kafka resources will be deployed."
        domainSuffix:
          title: Domain Suffix
          type: string
          ui:placeholder: "idp-test.deva.esc.esetrs.cz"
          ui:help: "Enter the DNS domain suffix used for Kafka broker access (e.g. idp-test.deva.esc.esetrs.cz)."
          default: idp-test.deva.esc.esetrs.cz
        kafkaVersion:
          title: Kafka Version
          type: string
          ui:placeholder: "3.8.0"
          ui:help: "Kafka version to deploy. Use a version compatible with your workload and Strimzi."
          default: "3.8.0"

    # Page 2: Storage Configuration
    - title: Storage Configuration
      properties:
        storage:
          title: Storage Configuration
          type: object
          properties:
            size:
              title: Storage Size
              type: string
              ui:placeholder: "10Gi"
              ui:help: "Specify the persistent volume size to allocate for Kafka storage (e.g. 10Gi)."
              default: "10Gi"
            deleteClaim:
              title: Delete PVC on cluster deletion
              type: boolean
              ui:help: "If enabled, Kafka PVCs will be deleted when the cluster is deleted."
              default: false

    # Page 3: Topics Configuration
    - title: Kafka Topics
      properties:
        topics:
          title: Kafka Topics
          type: array
          ui:placeholder: "topic1"
          ui:help: "Define the list of Kafka topics to create in this cluster."
          items:
            type: object
            required:
              - name
              - partitions
              - replicas
            properties:
              name:
                type: string
                ui:placeholder: "user-events"
                ui:help: "Name of the Kafka topic (must be unique within the cluster)."
              partitions:
                type: integer
                default: 1
                ui:placeholder: 1
                ui:help: "Number of partitions for this topic."
              replicas:
                type: integer
                default: 1
                ui:placeholder: 1
                ui:help: "Replication factor for this topic (number of brokers each partition is stored on)."

    # Page 4: Users Configuration
    - title: Kafka Users
      properties:
        users:
          title: Kafka Users
          type: array
          ui:placeholder: "alice"
          ui:help: "Provide a list of users to provision for Kafka authentication and authorization."
          items:
            type: object
            required:
              - name
              - type
              - topics
            properties:
              name:
                type: string
                ui:placeholder: "alice"
                ui:help: "Username for Kafka authentication (usually matches their application/service)."
              type:
                type: string
                enum: [tls]
                default: tls
                ui:help: "Authentication type for this user. Only 'tls' supported."
              topics:
                type: array
                items:
                  type: string
                ui:placeholder: "user-events"
                ui:help: "Specify which topics this user is allowed to access."

    # Page 5: Network Configuration
    - title: Network Configuration
      properties:
        listeners:
          title: Kafka Listeners
          type: object
          properties:
            internal:
              type: object
              properties:
                port:
                  type: integer
                  default: 9292
            nodeport:
              type: object
              properties:
                enabled:
                  type: boolean
                  default: true
                bootstrapNodePort:
                  type: integer
                  default: 32100
            ingress:
              type: object
              properties:
                enabled:
                  type: boolean
                  default: true
                ingressClass:
                  type: string
                  default: ec-platform
    
    # Page 6: Repository Configuration
    - title: Repository Configuration
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
      properties:
        gitlabUrl:
          title: GitLab URL
          type: string
          ui:placeholder: "gitlab.cluster.eset.corp"
          ui:help: "Select your GitLab instance for storing Kafka and topic/user configuration repositories."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: Environment
          type: string
          ui:placeholder: "deva"
          ui:help: "Choose the deployment environment for this Kafka cluster (deva for development, prod for production)."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: Project Name
          type: string
          ui:placeholder: "sigproject"
          ui:help: "Enter the GitLab project/group name that will own these repositories."
          default: "sigproject"
        appsRepoName:
          title: Apps Repository Name
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage application definitions."
          default: "backstage-apps"
        argocdRepoName:
          title: ArgoCD Repository Name
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository name for ArgoCD configuration files."
          default: "backstage-argocd-apps"

  steps:
    - id: generate-kafka-resources
      name: Generating Kafka Resource Manifests
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-cluster.yaml
        targetPath: ./output/kafka-cluster.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          domainSuffix: ${{ parameters.domainSuffix | lower }}
          kafkaVersion: ${{ parameters.kafkaVersion }}
          storage: ${{ parameters.storage }}
          listeners: ${{ parameters.listeners }}
          env: ${{ parameters.env }}

    - id: generate-kafka-namespace
      name: Generating Kafka Namespace Manifests
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-namespace.yaml
        targetPath: ./output/kafka-namespace.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

  # Create Kafka Cluster Component
    - id: create-cluster-component
      name: Create Kafka Cluster Component
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-cluster-component.yaml
        targetPath: ./output/kafka-cluster-component.yaml
        values:
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          namespace: ${{ parameters.namespace | lower }}
          domainSuffix: ${{ parameters.domainSuffix }}
          kafkaVersion: ${{ parameters.kafkaVersion }}
          topics: ${{ parameters.topics }}
          users: ${{ parameters.users }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    # Create Topic Components and Resources
    - id: create-topic-resources
      name: Create Kafka Topics
      each: ${{ parameters.topics }}
      if: ${{ parameters.topics }}
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-topic.yaml
        targetPath: ./output/topics/resources/${{ each.value.name | lower }}.yaml
        values:
          topicName: ${{ each.value.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          partitions: ${{ each.value.partitions }}
          replicas: ${{ each.value.replicas }}

    - id: create-topic-components
      name: Create Topic Components
      each: ${{ parameters.topics }}
      if: ${{ parameters.topics }}
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-topic-component.yaml
        targetPath: ./output/topics/components/${{ each.value.name | lower }}-component.yaml
        values:
          topicName: ${{ each.value.name }}
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          partitions: ${{ each.value.partitions }}
          replicas: ${{ each.value.replicas }}
          env: ${{ parameters.env }}

    # Create User Components and Resources
    - id: create-user-resources
      name: Create Kafka Users
      each: ${{ parameters.users }}
      if: ${{ parameters.users }}
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-user.yaml
        targetPath: ./output/users/resources/${{ each.value.name | lower }}.yaml
        values:
          userName: ${{ each.value.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          type: ${{ each.value.type }}
          topics: ${{ each.value.topics }}

    - id: create-user-components
      name: Create User Components
      each: ${{ parameters.users }}
      if: ${{ parameters.users }}
      action: fetch:template:file
      input:
        url: ./kafka-resources/kafka-user-component.yaml
        targetPath: ./output/users/components/${{ each.value.name | lower }}-component.yaml
        values:
          userName: ${{ each.value.name | lower }}
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    - id: create-commit-for-kafka-config
      name: Create Commit for Kafka Config to GitLab
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/kafka/${{ parameters.kafkaClusterName | lower }}
        commitMessage: "Add configuration for Kafka cluster '${{ parameters.kafkaClusterName | lower }}'"

    - id: register-kafka-component
      name: Registering the Kafka Component
      action: catalog:register
      input:
        repoContentsUrl: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/blob/main/
        catalogInfoPath: /${{ parameters.env }}/kafka/${{ parameters.kafkaClusterName | lower }}/kafka-cluster-component.yaml

    - id: register-kafka-topic-component
      name: Registering the Kafka Topic Components
      action: catalog:register
      each: ${{ parameters.topics }}
      input:
        repoContentsUrl: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/blob/main/
        catalogInfoPath: /${{ parameters.env }}/kafka/${{ parameters.kafkaClusterName | lower }}/topics/components/${{ each.value.name | lower }}-component.yaml

    - id: register-kafka-user-component
      name: Registering the Kafka User Components
      action: catalog:register
      each: ${{ parameters.users }}
      input:
        repoContentsUrl: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/blob/main/
        catalogInfoPath: /${{ parameters.env }}/kafka/${{ parameters.kafkaClusterName | lower }}/users/components/${{ each.value.name | lower }}-component.yaml

    - id: generate-argocd-config
      name: Generating Argo CD Application Configuration
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          kafkaClusterName: ${{ parameters.kafkaClusterName | lower }}
          namespace: ${{ parameters.namespace | lower }}
          kafkaResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.projectName }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        targetPath: ./argocd-config

    - id: rename-argocd-config
      name: Rename ArgoCD Application File
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.kafkaClusterName | lower }}-kafka-application.yaml

    - id: create-mr-for-argocd-config
      name: Create MR for Argo CD Config to GitLab
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/${{ parameters.kafkaClusterName | lower }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: ${{ parameters.env }}/kafka
        description: "Add Argo CD configuration for Kafka cluster '${{ parameters.kafkaClusterName | lower }}'"
        title: "Add Kafka cluster '${{ parameters.kafkaClusterName | lower }}' configuration"

  output:
    links:
      - title: Open the Kafka Resources Repository
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/kafka/${{ parameters.kafkaClusterName | lower }}
      - title: Open the Kafka Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-kafka-component'].output.entityRef }}
      - title: View Merge Request for Argo CD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}