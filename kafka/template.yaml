apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: kafka-instance-template
  title: Create a Kafka Instance using Strimzi
  description: This template creates a Kafka instance using the Strimzi operator.
  tags:
    - kafka
    - strimzi
    - kubernetes
    - argocd
spec:
  owner: your-team
  type: service

  parameters:
    # Page 1: Kafka Parameters
    - title: Kafka Configuration
      required:
        - kafkaClusterName
        - replicas
        - controllerReplicas
        - kafkaVersion
        - kafkaMetadataVersion
      properties:
        kafkaClusterName:
          title: Kafka Cluster Name
          type: string
          description: The name to assign to your Kafka cluster.
          default: my-cluster
        replicas:
          title: Number of Broker Replicas
          type: integer
          description: The number of Kafka broker replicas.
          default: 3
        controllerReplicas:
          title: Number of Controller Replicas
          type: integer
          description: The number of Kafka controller replicas.
          default: 3
        kafkaVersion:
          title: Kafka Version
          type: string
          description: The version of Kafka to deploy.
          default: "3.9.0"
        kafkaMetadataVersion:
          title: Kafka Metadata Version
          type: string
          description: The metadata version for Kafka.
          default: "3.9-IV0"
        description:
          title: Description
          type: string
          description: A description for your Kafka instance.
          default: Kafka instance generated by Backstage

    # Page 2: GitLab Repository Configuration
    # - title: GitLab Repository Configuration
    #   required:
    #     - repoUrl
    #   properties:
    #     repoUrl:
    #       title: Repository Location
    #       type: string
    #       ui:field: RepoUrlPicker
    #       ui:options:
    #         allowedOwners:
    #           - SigProject
    #         allowedHosts:
    #           - gitlab.cluster.eset.corp

  steps:
    - id: generate-kafka-resources
      name: Generating Kafka Resource Manifests
      action: fetch:template
      input:
        url: ./kafka-resources
        values:
          kafkaClusterName: ${{ parameters.kafkaClusterName }}
          replicas: ${{ parameters.replicas }}
          controllerReplicas: ${{ parameters.controllerReplicas }}
          kafkaVersion: ${{ parameters.kafkaVersion }}
          kafkaMetadataVersion: ${{ parameters.kafkaMetadataVersion }}
          namespace: kafka

    # - id: publish-kafka-resources
    #   name: Publishing Kafka Resources to GitLab
    #   action: publish:gitlab
    #   input:
    #     repoUrl: ${{ parameters.repoUrl }}
    #     description: ${{ parameters.description }}

    - id: create-commit-for-kafka-config
      name: Create Commit for Kafka Config to GitLab
      action: gitlab:repo:push
      input:
        repoUrl: gitlab.cluster.eset.corp?repo=backstage-apps&owner=sigproject
        branchName: main
        # sourcePath: ./argocd-config
        targetPath: kafka/${{ parameters.kafkaClusterName }}
        commitMessage: "Add configuration for Kafka cluster '${{ parameters.kafkaClusterName }}'"
  

    - id: register-kafka-component
      name: Registering the Kafka Component
      action: catalog:register
      input:
        repoContentsUrl: https://gitlab.cluster.eset.corp/sigproject/backstage-apps/-/blob/main/
        catalogInfoPath: /kafka/${{ parameters.kafkaClusterName }}/catalog-info.yaml

    - id: generate-argocd-config
      name: Generating Argo CD Application Configuration
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          kafkaClusterName: ${{ parameters.kafkaClusterName }}
          namespace: kafka
          kafkaResourcesRepoUrl: ${{ steps['publish-kafka-resources'].output.remoteUrl }}
        targetPath: ./argocd-config

    - id: rename-argocd-config
      name: Rename ArgoCD Application File
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.kafkaClusterName }}-kafka-application.yaml

    - id: create-mr-for-argocd-config
      name: Create MR for Argo CD Config to GitLab
      action: publish:gitlab:merge-request
      input:
        repoUrl: gitlab.cluster.eset.corp?**********************apps&owner=sigproject
        branchName: feature/${{ parameters.kafkaClusterName }}-create-${{ context.task.id }}
        # branchName: feature/${{ parameters.kafkaClusterName }}-create-10
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: kafka
        description: "Add Argo CD configuration for Kafka cluster '${{ parameters.kafkaClusterName }}'"
        title: "Add Kafka cluster '${{ parameters.kafkaClusterName }}' configuration"

  output:
    links:
      - title: Open the Kafka Resources Repository
        url: ${{ steps['create-commit-for-kafka-config'].output.projectPath }}
      - title: Open the Kafka Resources Repository2
        url: ${{ steps['create-commit-for-kafka-config'].output.projectId }}
      - title: Open the Kafka Resources Repository3
        url: https://gitlab.cluster.eset.corp/sigproject/backstage-apps/-/tree/main/kafka/${{ parameters.kafkaClusterName }}
      - title: Open the Kafka Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-kafka-component'].output.entityRef }}
      - title: View Merge Request for Argo CD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
