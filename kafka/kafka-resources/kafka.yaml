# KafkaNodePool for Controller

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: controller
  namespace: ${{ values.namespace }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  replicas: ${{ values.controllerReplicas }}
  roles:
    - controller
  storage:
    type: jbod
    volumes:
      - id: 0
        type: ephemeral
        kraftMetadata: shared

---

# KafkaNodePool for Broker

apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: broker
  namespace: ${{ values.namespace }}
  labels:
    strimzi.io/cluster: ${{ values.kafkaClusterName }}
spec:
  replicas: ${{ values.replicas }}
  roles:
    - broker
  storage:
    type: jbod
    volumes:
      - id: 0
        type: ephemeral
        kraftMetadata: shared

---

# Kafka Cluster

apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: ${{ values.kafkaClusterName }}
  namespace: ${{ values.namespace }}
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: ${{ values.kafkaVersion }}
    metadataVersion: ${{ values.kafkaMetadataVersion }}
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
    config:
      offsets.topic.replication.factor: ${{ values.controllerReplicas }}
      transaction.state.log.replication.factor: ${{ values.controllerReplicas }}
      transaction.state.log.min.isr: 2
      default.replication.factor: ${{ values.replicas }}
      min.insync.replicas: 2
  entityOperator:
    topicOperator: {}
    userOperator: {}
