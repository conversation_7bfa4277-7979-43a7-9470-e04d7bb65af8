apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.kafkaClusterName }}
  namespace: argocd
spec:
  project: default
  source:
    repoURL: ${{ values.kafkaResourcesRepoUrl }}
    targetRevision: HEAD
    path: .
  destination:
    server: https://kubernetes.default.svc
    namespace: kafka
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
    retry:
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 10m
