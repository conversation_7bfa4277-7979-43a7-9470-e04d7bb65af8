apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: "${{ values.name }}"
  title: "${{ values.name }}"
  namespace: "${{ values.env }}"
  annotations:
    backstage.io/kubernetes-id: "${{ values.name }}"
    backstage.io/kubernetes-namespace: "${{ values.namespace }}"
    argocd/app-name: "${{ values.name }}"
    keydb.mode: "${{ values.keydbMode }}"
    environment: "${{ values.env }}"
  tags:
    - keydb
    - database
    - redis
    - kubernetes
    - argocd
    - ${{ values.env }}
  links:
    - url: "https://${{ values.gitlabUrl }}/${{ values.projectName }}/${{ values.appsRepoName }}/-/tree/main/${{ values.env }}/keydb/${{ values.name }}"
      title: "KeyDB Config Repository"
    - url: "https://keydb-monitoring.example.com/${{ values.name }}"
      title: "KeyDB Monitoring Dashboard"
spec:
  type: keydb-instance
  owner: default/eset
  lifecycle: experimental
  system: keydb
  environment: "${{ values.env }}"