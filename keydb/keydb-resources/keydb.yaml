apiVersion: keydb.krestomat.io/v1alpha1
kind: Keydb
metadata:
  name: ${{ parameters.name }}
  namespace: ${{ parameters.namespace }}
spec:
  # keydbMode: one of 'standalone', 'multimaster', 'custom'
  keydbMode: '{{ parameters.keydbMode }}'

  # By default, 'standalone' is 1 replica and 'multimaster' is 3
  # (You can handle replica counts in the operator or add your own logic if needed.)

  {{#if parameters.enableResourceLimits}}
  keydbResourceLimits: true
  keydbResourceLimitsCpu: ${{ parameters.resourceLimitsCpu }}
  keydbResourceLimitsMemory: ${{ parameters.resourceLimitsMemory }}
  {{/if}}

  # Assign the PVC size for each replica
  keydbPvcDataSize: ${{ parameters.pvcDataSize }}

  # Uncomment or conditionally insert autoexpansion if needed
  {{#if parameters.enableAutoexpansion}}
  # IMPORTANT:
  # * The Kubernetes cluster and PVC MUST support expansion of volumes
  # * In older cluster versions, pods require a restart for expansions if the
  #   'ExpandInUsePersistentVolumes' feature gate is false.
  keydbPvcDataAutoexpansion: true
  keydbPvcDataAutoexpansionIncrementGib: ${{ parameters.autoexpansionIncrementGib }}
  keydbPvcDataAutoexpansionCapGib: ${{ parameters.autoexpansionCapGib }}
  {{/if}}

  # Additional custom KeyDB configuration:
  keydbExtraConfig: |
    {{ parameters.keydbExtraConfig }}