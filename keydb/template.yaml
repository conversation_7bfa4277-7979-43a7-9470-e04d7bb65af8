apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: keydb-crd-template
  title: "Generate KeyDB CRD (with keydbMode selection)"
  description: "A Scaffolder template for creating a KeyDB CRD with selectable mode and sizing hints"
  tags:
    - keydb
    - database
    - redis
spec:
  owner: eset
  type: service
  parameters:
    - title: "Basic Settings"
      properties:
        name:
          title: "KeyDB Name"
          type: string
          description: "Name of the KeyDB CRD (metadata.name)"
          default: "keydb-sample"
        namespace:
          title: "Namespace"
          type: string
          description: "Kubernetes namespace in which the KeyDB CRD will be created"
          default: "keydb-operator"
        keydbMode:
          title: "KeyDB Mode"
          type: string
          enum:
            - standalone
            - multimaster
            - custom
          description: "One of 'standalone', 'multimaster', 'custom'. By default, 'standalone' is 1 replica, 'multimaster' is 3 replicas."
          default: "multimaster"
      required:
        - name
        - namespace
        - keydbMode

    - title: "Resource Limits"
      properties:
        enableResourceLimits:
          title: "Enable resource limits?"
          type: boolean
          default: true
        resourceLimitsCpu:
          title: "CPU limit"
          type: number
          description: "CPU limit (cores) if resource limits are enabled."
          default: 1
        resourceLimitsMemory:
          title: "Memory limit"
          type: string
          description: "Memory limit if resource limits are enabled (e.g., 1Gi)."
          default: "1Gi"

    - title: "Storage Configuration"
      properties:
        pvcDataSize:
          title: "PVC Data Size"
          type: string
          description: "Size of the persistent volume claim (e.g., 1Gi)."
          default: "1Gi"
        enableAutoexpansion:
          title: "Enable PVC autoexpansion?"
          type: boolean
          default: false
        autoexpansionIncrementGib:
          title: "Autoexpansion increment (GiB)"
          type: number
          description: "Increase PVC by this many GiB when storage usage < 20% free. (Requires 'enableAutoexpansion' = true.)"
          default: 5
        autoexpansionCapGib:
          title: "Autoexpansion cap (GiB)"
          type: number
          description: "Maximum total size after expansions. (Requires 'enableAutoexpansion' = true.)"
          default: 25

    - title: "Extra KeyDB Config"
      properties:
        keydbExtraConfig:
          title: "Additional KeyDB Configuration"
          type: string
          description: "Extra KeyDB config directives (multi-line). Example: 'maxmemory 900mb\\nmaxmemory-policy allkeys-lru'"
          default: |
            maxmemory 900mb
            maxmemory-policy allkeys-lru

    # Add repository configuration like other templates
    - title: "Repository Configuration"
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
      properties:
        gitlabUrl:
          title: "GitLab URL"
          type: string
          description: "GitLab instance URL"
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: "Environment"
          type: string
          description: "Environment where the KeyDB instance is deployed"
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: "Project Name"
          type: string
          description: "GitLab project name (owner)"
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          description: "Repository name for Backstage apps"
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          description: "Repository name for ArgoCD applications"
          default: "backstage-argocd-apps"

  steps:
    - id: generate-keydb-manifest
      name: "Generate KeyDB manifest"
      action: fetch:template:file
      input:
        url: ./keydb/keydb-resources/keydb.yaml
        targetPath: ./output/keydb.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          keydbMode: ${{ parameters.keydbMode }}
          enableResourceLimits: ${{ parameters.enableResourceLimits }}
          resourceLimitsCpu: ${{ parameters.resourceLimitsCpu }}
          resourceLimitsMemory: ${{ parameters.resourceLimitsMemory }}
          pvcDataSize: ${{ parameters.pvcDataSize }}
          enableAutoexpansion: ${{ parameters.enableAutoexpansion }}
          autoexpansionIncrementGib: ${{ parameters.autoexpansionIncrementGib }}
          autoexpansionCapGib: ${{ parameters.autoexpansionCapGib }}
          keydbExtraConfig: ${{ parameters.keydbExtraConfig }}
          env: ${{ parameters.env }}

    # Generate namespace manifest
    - id: generate-namespace
      name: "Generate KeyDB namespace"
      action: fetch:template:file
      input:
        url: ./keydb/keydb-resources/keydb-namespace.yaml
        targetPath: ./output/keydb-namespace.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

    # Create component for catalog
    - id: create-keydb-component
      name: "Create KeyDB Component"
      action: fetch:template:file
      input:
        url: ./keydb/keydb-resources/keydb-component.yaml
        targetPath: ./output/keydb-component.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          keydbMode: ${{ parameters.keydbMode }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    # Push to GitLab
    - id: create-commit-for-keydb-config
      name: "Create Commit for KeyDB Config to GitLab"
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/keydb/${{ parameters.name | lower }}
        commitMessage: "Add configuration for KeyDB instance '${{ parameters.name | lower }}'"

    # Register in catalog
    - id: register-keydb-component
      name: "Register KeyDB Component in Catalog"
      action: catalog:register
      input:
        catalogInfoUrl: ${{ ("https://" + parameters.gitlabUrl + "/" + parameters.projectName + "/" + parameters.appsRepoName + "/-/blob/main/" + parameters.env + "/keydb/" + parameters.name | lower + "/keydb-component.yaml") | string }}

    # Generate ArgoCD application
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./keydb/argocd-config
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          keydbResourcesRepoUrl: 'ssh://git@${{ parameters.gitlabUrl }}:7999/${{ parameters.projectName }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        targetPath: ./argocd-config

    # Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.name | lower }}-keydb-application.yaml

    # Create MR for ArgoCD config
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/${{ parameters.name | lower }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: ${{ parameters.env }}/keydb
        description: "Add ArgoCD configuration for KeyDB instance '${{ parameters.name | lower }}'"
        title: "Add KeyDB instance '${{ parameters.name | lower }}' configuration"

  output:
    links:
      - title: Open the KeyDB Resources Repository
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/keydb/${{ parameters.name | lower }}
      - title: Open the KeyDB Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-keydb-component'].output.entityRef }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
    keydbName: ${{ parameters.name | lower }}
    namespace: ${{ parameters.namespace | lower }}
