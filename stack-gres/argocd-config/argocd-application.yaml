apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ${{ values.name }}
  namespace: ec-argocd
spec:
  project: default
  source:
    repoURL: ${{ values.postgresResourcesRepoUrl }}
    targetRevision: HEAD
    path: ${{ values.env }}/postgres/${{ values.name }}
    directory:
      include: "{*.yml,*.yaml}"
      exclude: '*-component.yaml'
      recurse: true
  destination:
    name: idp-test
    namespace: ${{ values.namespace }}
  syncPolicy:
    automated: {}
    syncOptions:
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
