# StackGres SGCluster Template

This template creates a StackGres PostgreSQL cluster in Kubernetes using the SGCluster custom resource, GitLab for configuration storage, and ArgoCD for deployment.

## Template Overview

The template has been simplified to focus on the most important configuration options for a PostgreSQL cluster:

1. **Basic Configuration**
   - Cluster name and namespace
   - StackGres and PostgreSQL versions

2. **Cluster Configuration**
   - Number of replicas
   - Storage size
   - Service type

3. **PostgreSQL Configuration**
   - Basic PostgreSQL parameters (shared_buffers, max_connections)
   - Extensions (as an array of strings)

4. **Advanced Options**
   - SSL configuration
   - Backup restoration

5. **Repository Configuration**
   - GitLab URL and project details
   - Environment (deva, prod)
   - Repository names for apps and ArgoCD configurations

## Usage

1. Select the template in Backstage
2. Fill in the required parameters
3. Click "Create" to deploy the SGCluster

## Template Structure

The template follows a GitOps approach using GitLab and ArgoCD:

1. **Generate Resources**: Creates SGCluster, namespace, and component manifests
2. **Push to GitLab**: Pushes the configuration to a GitLab repository
3. **Register in Catalog**: Registers the PostgreSQL cluster as a component in the Backstage catalog
4. **Create ArgoCD Application**: Creates an ArgoCD application to deploy the resources

This approach follows the same pattern as the Kafka template, ensuring consistency across infrastructure templates.

## Validation Notes

The template has been simplified from the original complex version to:

1. Reduce the number of parameters to the most essential ones
2. Use proper array type for extensions instead of comma-separated strings
3. Use GitOps with GitLab and ArgoCD for deployment
4. Avoid complex conditionals in YAML templates
5. Focus on the most commonly used configuration options

This approach follows Backstage best practices by:

1. Using the `fetch:template:file` action to render templates with values
2. Using GitLab for configuration storage with `gitlab:repo:push`
3. Creating ArgoCD applications with `publish:gitlab:merge-request`
4. Registering components in the catalog with `catalog:register`

## Kubernetes Resources

The template creates the following Kubernetes resources:

- Namespace for the PostgreSQL cluster
- SGCluster custom resource
- Backstage component for the PostgreSQL cluster
- ArgoCD application for deployment

## Extensions

The template allows you to specify PostgreSQL extensions as an array. The default is `pg_stat_statements`, but you can add more as needed.

## GitOps Workflow

The template follows a GitOps workflow:

1. Configuration is stored in GitLab
2. ArgoCD watches the GitLab repository for changes
3. ArgoCD automatically applies changes to the Kubernetes cluster
4. Backstage provides a unified view of the PostgreSQL clusters

## Advanced Configuration

For more advanced configuration options, you can modify the template to include additional parameters as needed. The StackGres operator supports many more configuration options than what is included in this simplified template.
