apiVersion: stackgres.io/v1
kind: SGCluster
metadata:
  name: ${{ values.name }}
  namespace: ${{ values.namespace }}
  labels:
    backstage.io/kubernetes-id: ${{ values.name }}
spec:
  metadata:
    labels:
      clusterPods:
        backstage.io/kubernetes-id: ${{ values.name }}
      services:
        backstage.io/kubernetes-id: ${{ values.name }}


  # PostgreSQL configuration
  postgres:
    version: "${{ values.pgVersion }}"
    parameters:
      shared_buffers: "${{ values.sharedBuffers }}"
      max_connections: ${{ values.maxConnections | int }}

    # Extensions - using a fixed list for simplicity
    extensions:
      - name: pg_stat_statements

  # Replication configuration
  instances: ${{ values.replicas }}

  # Service configuration
  service:
    primary:
      type: "${{ values.serviceType }}"
    replicas:
      type: "${{ values.serviceType }}"

  # Storage configuration
  pods:
    persistentVolume:
      size: ${{ values.storageSize }}

  # Instance profile configuration
  sgInstanceProfile: ${{ values.name }}

  # Managed SQL scripts
  managedSql:
    scripts:
      - sgScript: ${{ values.name }}-createdb
