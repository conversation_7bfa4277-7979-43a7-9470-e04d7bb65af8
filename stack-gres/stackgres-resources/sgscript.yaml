apiVersion: stackgres.io/v1
kind: SGScript
metadata:
  name: ${{ values.name }}-createdb
  namespace: ${{ values.namespace }}
spec:
  scripts:
  - name: create-user
    script: |
      create user ${{ values.database }}_user with password '${{ values.password }}';
  - name: create-database
    script: |
      create database ${{ values.database }} owner ${{ values.database }}_user encoding 'UTF8' locale 'en_US.UTF-8' template template0;