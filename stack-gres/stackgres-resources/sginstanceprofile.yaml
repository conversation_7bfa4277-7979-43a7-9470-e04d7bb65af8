apiVersion: stackgres.io/v1
kind: SGInstanceProfile
metadata:
  name: ${{ values.name }}
  namespace: ${{ values.namespace }}
spec:
  containers:
    backup.create-backup:
      cpu: "1"
      memory: 256Mi
    cluster-controller:
      cpu: 250m
      memory: 512Mi
    dbops.run-dbops:
      cpu: "1"
      memory: 256Mi
    dbops.set-dbops-result:
      cpu: "1"
      memory: 256Mi
    envoy:
      cpu: 250m
      memory: 64Mi
    fluent-bit:
      cpu: 63m
      memory: 64Mi
    fluentd:
      cpu: 250m
      memory: 2Gi
    pgbouncer:
      cpu: 250m
      memory: 64Mi
    postgres-util:
      cpu: 63m
      memory: 64Mi
    prometheus-postgres-exporter:
      cpu: 63m
      memory: 256Mi
  cpu: "1"
  initContainers:
    cluster-reconciliation-cycle:
      cpu: "1"
      memory: 2Gi
    dbops.set-dbops-running:
      cpu: "1"
      memory: 256Mi
    distributedlogs-reconciliation-cycle:
      cpu: "1"
      memory: 2Gi
    major-version-upgrade:
      cpu: "1"
      memory: 2Gi
    pgbouncer-auth-file:
      cpu: "1"
      memory: 2Gi
    relocate-binaries:
      cpu: "1"
      memory: 2Gi
    reset-patroni:
      cpu: "1"
      memory: 2Gi
    setup-arbitrary-user:
      cpu: "1"
      memory: 2Gi
    setup-scripts:
      cpu: "1"
      memory: 2Gi
  memory: 2Gi
  requests:
    containers:
      backup.create-backup:
        cpu: "1"
        memory: 256Mi
      cluster-controller:
        cpu: 250m
        memory: 512Mi
      dbops.run-dbops:
        cpu: "1"
        memory: 256Mi
      dbops.set-dbops-result:
        cpu: "1"
        memory: 256Mi
      envoy:
        cpu: 250m
        memory: 64Mi
      fluent-bit:
        cpu: 63m
        memory: 64Mi
      fluentd:
        cpu: 250m
        memory: 2Gi
      pgbouncer:
        cpu: 250m
        memory: 64Mi
      postgres-util:
        cpu: 63m
        memory: 64Mi
      prometheus-postgres-exporter:
        cpu: 63m
        memory: 256Mi
    cpu: "1"
    initContainers:
      cluster-reconciliation-cycle:
        cpu: "1"
        memory: 2Gi
      dbops.set-dbops-running:
        cpu: "1"
        memory: 256Mi
      distributedlogs-reconciliation-cycle:
        cpu: "1"
        memory: 2Gi
      major-version-upgrade:
        cpu: "1"
        memory: 2Gi
      pgbouncer-auth-file:
        cpu: "1"
        memory: 2Gi
      relocate-binaries:
        cpu: "1"
        memory: 2Gi
      reset-patroni:
        cpu: "1"
        memory: 2Gi
      setup-arbitrary-user:
        cpu: "1"
        memory: 2Gi
      setup-scripts:
        cpu: "1"
        memory: 2Gi
    memory: 2Gi
