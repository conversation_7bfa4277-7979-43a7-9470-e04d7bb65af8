apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: stackgres-sgcluster
  title: "StackGres SGCluster Creation"
  description: "Backstage Scaffolder template for deploying a StackGres PostgreSQL cluster using GitLab and ArgoCD"
  tags:
    - stackgres
    - postgres
    - argocd
    - gitlab
spec:
  owner: default/eset
  type: service

  parameters:
    - title: "Basic Configuration"
      required:
        - name
        - namespace
        - pgVersion
        - database
      properties:
        name:
          title: "SGCluster Name"
          type: string
          ui:placeholder: "my-postgres-sgcluster"
          ui:help: "Provide a unique name for your StackGres Cluster resource (SGCluster)."
          default: "postgresql-sgcluster"
        namespace:
          title: "Namespace"
          type: string
          ui:placeholder: "postgresql-sgcluster"
          ui:help: "Specify the Kubernetes namespace where this cluster will be created."
          default: "postgresql-sgcluster"
        database:
          title: "Database Name"
          type: string
          ui:placeholder: "mydatabase"
          ui:help: "The database name that will be created in the cluster."
          default: "mydatabase"
        pgVersion:
          title: "PostgreSQL Version"
          type: string
          ui:placeholder: "13"
          ui:help: "Select the major version of PostgreSQL to deploy."
          default: "13"
          enum:
            - "12"
            - "13"
            - "14"
            - "15"

    - title: "Cluster Configuration"
      properties:
        replicas:
          title: "Number of Replicas"
          type: number
          minimum: 1
          ui:placeholder: 3
          ui:help: "Set the total number of PostgreSQL instances, including the primary."
          default: 3
        storageSize:
          title: "Storage Size"
          type: string
          ui:placeholder: "1Gi"
          ui:help: "Size of the Persistent Volume Claim for PostgreSQL data (e.g., 1Gi, 10Gi)."
          default: "1Gi"
        serviceType:
          title: "Service Type"
          type: string
          ui:placeholder: "ClusterIP"
          ui:help: "Choose the Kubernetes Service type to expose the primary instance."
          enum:
            - "ClusterIP"
            - "NodePort"
            - "LoadBalancer"
          default: "ClusterIP"

    - title: "PostgreSQL Configuration"
      properties:
        sharedBuffers:
          title: "shared_buffers"
          type: string
          ui:placeholder: "128MB"
          ui:help: "Amount of memory allocated for PostgreSQL shared_buffers parameter."
          default: "128MB"
        maxConnections:
          title: "max_connections"
          type: number
          ui:placeholder: 100
          ui:help: "Maximum number of concurrent PostgreSQL connections allowed."
          default: 100
        password:
          title: "Database User Password"
          type: string
          ui:placeholder: "secure_password"
          ui:help: "Password for the database user. Will be used in the SGScript."
          default: "admin123"
        extensions:
          title: "PostgreSQL Extensions"
          type: array
          ui:placeholder: "pg_stat_statements"
          ui:help: "List of PostgreSQL extensions to enable in this cluster (e.g. pg_stat_statements, citext)."
          items:
            type: string
          default: ["pg_stat_statements"]
          ui:options:
            addable: true
            orderable: false
            removable: true

    - title: "Advanced Options"
      properties:
        sslEnabled:
          title: "Enable SSL"
          type: boolean
          ui:help: "Enable SSL/TLS for secure PostgreSQL connections."
          default: false
        sslCertSecretName:
          title: "SSL Certificate Secret"
          type: string
          ui:placeholder: "my-postgres-ssl-secret"
          ui:help: "Name of the Kubernetes Secret containing the SSL certificate, if enabling SSL."
          default: ""
        backupEnabled:
          title: "Restore from Backup"
          type: boolean
          ui:help: "Select to restore this cluster from an existing backup."
          default: false
        backupName:
          title: "Backup Name"
          type: string
          ui:placeholder: "my-sgbackup"
          ui:help: "Enter the name of the existing SGBackup resource to restore from."
          default: ""

    - title: "Repository Configuration"
      required:
        - gitlabUrl
        - projectName
        - appsRepoName
        - argocdRepoName
      properties:
        gitlabUrl:
          title: "GitLab URL"
          type: string
          ui:placeholder: "gitlab.cluster.eset.systems"
          ui:help: "Select your GitLab instance used to store configuration repositories."
          default: "gitlab.cluster.eset.corp"
          enum:
            - "gitlab.cluster.eset.corp"
            - "gitlab.cluster.eset.systems"
        env:
          title: "Environment"
          type: string
          ui:placeholder: "deva"
          ui:help: "Target environment for this cluster deployment: deva (development) or prod (production)."
          default: "deva"
          enum:
            - "deva"
            - "prod"
        projectName:
          title: "Project Name"
          type: string
          ui:placeholder: "sigproject"
          ui:help: "Enter the name of the GitLab project (repository owner or group)."
          default: "sigproject"
        appsRepoName:
          title: "Apps Repository Name"
          type: string
          ui:placeholder: "backstage-apps"
          ui:help: "Repository name for Backstage app component definitions."
          default: "backstage-apps"
        argocdRepoName:
          title: "ArgoCD Repository Name"
          type: string
          ui:placeholder: "backstage-argocd-apps"
          ui:help: "Repository name used for ArgoCD application manifests."
          default: "backstage-argocd-apps"

  steps:
    # 1a. Generate SGCluster manifest
    - id: generate-sgcluster
      name: "Generate SGCluster manifest"
      action: fetch:template:file
      input:
        url: ./stackgres-resources/sgcluster-base.yaml
        targetPath: ./output/sgcluster.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          database: ${{ parameters.database }}
          pgVersion: ${{ parameters.pgVersion }}
          sharedBuffers: ${{ parameters.sharedBuffers }}
          maxConnections: ${{ parameters.maxConnections }}
          replicas: ${{ parameters.replicas }}
          serviceType: ${{ parameters.serviceType }}
          storageSize: ${{ parameters.storageSize }}

    # 1b. Generate SGInstanceProfile manifest
    - id: generate-sginstanceprofile
      name: "Generate SGInstanceProfile manifest"
      action: fetch:template:file
      input:
        url: ./stackgres-resources/sginstanceprofile.yaml
        targetPath: ./output/sginstanceprofile.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}

    # 1c. Generate SGScript for user and database creation
    - id: generate-sgscript
      name: "Generate SGScript for user and database creation"
      action: fetch:template:file
      input:
        url: ./stackgres-resources/sgscript.yaml
        targetPath: ./output/sgscript.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          password: ${{ parameters.password }}
          database: ${{ parameters.database }}

    # 2. Generate namespace manifest
    - id: generate-namespace
      name: "Generate namespace manifest"
      action: fetch:template:file
      input:
        url: ./stackgres-resources/postgres-namespace.yaml
        targetPath: ./output/postgres-namespace.yaml
        values:
          namespace: ${{ parameters.namespace | lower }}

    # 3. Create PostgreSQL Cluster Component
    - id: create-postgres-component
      name: "Create PostgreSQL Cluster Component"
      action: fetch:template:file
      input:
        url: ./stackgres-resources/postgres-component.yaml
        targetPath: ./output/postgres-component.yaml
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          database: ${{ parameters.database }}
          pgVersion: ${{ parameters.pgVersion }}
          gitlabUrl: ${{ parameters.gitlabUrl }}
          projectName: ${{ parameters.projectName }}
          appsRepoName: ${{ parameters.appsRepoName }}
          env: ${{ parameters.env }}

    # 4. Create commit for PostgreSQL config to GitLab
    - id: create-commit-for-postgres-config
      name: "Create Commit for PostgreSQL Config to GitLab"
      action: gitlab:repo:push
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.appsRepoName }}&owner=${{ parameters.projectName }}
        branchName: main
        sourcePath: ./output
        targetPath: /${{ parameters.env }}/postgres/${{ parameters.name | lower }}
        commitMessage: "Add configuration for PostgreSQL cluster '${{ parameters.name | lower }}'"

    # 5. Register the component in the catalog
    - id: register-postgres-component
      name: "Register PostgreSQL Component in Catalog"
      action: catalog:register
      input:
        catalogInfoUrl: ${{ ("https://" + parameters.gitlabUrl + "/" + parameters.projectName + "/" + parameters.appsRepoName + "/-/blob/main/" + parameters.env + "/postgres/" + parameters.name | lower + "/postgres-component.yaml") | string }}

    # 6. Generate ArgoCD application configuration
    - id: generate-argocd-config
      name: "Generate ArgoCD Application Configuration"
      action: fetch:template
      input:
        url: ./argocd-config
        values:
          name: ${{ parameters.name | lower }}
          namespace: ${{ parameters.namespace | lower }}
          postgresResourcesRepoUrl: 'ssh://*******************************:7999/${{ parameters.projectName }}/${{ parameters.appsRepoName }}.git'
          env: ${{ parameters.env }}
        targetPath: ./argocd-config

    # 7. Rename ArgoCD application file
    - id: rename-argocd-config
      name: "Rename ArgoCD Application File"
      action: fs:rename
      input:
        files:
          - from: ./argocd-config/argocd-application.yaml
            to: ./argocd-config/${{ parameters.name | lower }}-postgres-application.yaml

    # 8. Create MR for ArgoCD config to GitLab
    - id: create-mr-for-argocd-config
      name: "Create MR for ArgoCD Config to GitLab"
      action: publish:gitlab:merge-request
      input:
        repoUrl: ${{ parameters.gitlabUrl }}?repo=${{ parameters.argocdRepoName }}&owner=${{ parameters.projectName }}
        branchName: feature/${{ parameters.name | lower }}-create-${{ context.task.id }}
        targetBranchName: main
        sourcePath: ./argocd-config
        targetPath: ${{ parameters.env }}/postgres
        description: "Add ArgoCD configuration for PostgreSQL cluster '${{ parameters.name | lower }}'"
        title: "Add PostgreSQL cluster '${{ parameters.name | lower }}' configuration"

  output:
    links:
      - title: Open the PostgreSQL Resources Repository
        url: https://${{ parameters.gitlabUrl }}/${{ parameters.projectName }}/${{ parameters.appsRepoName }}/-/tree/main/${{ parameters.env }}/postgres/${{ parameters.name | lower }}
      - title: Open the PostgreSQL Component in Backstage
        icon: catalog
        entityRef: ${{ steps['register-postgres-component'].output.entityRef }}
      - title: View Merge Request for ArgoCD Config
        url: ${{ steps['create-mr-for-argocd-config'].output.mergeRequestUrl }}
    clusterName: ${{ parameters.name | lower }}
    namespace: ${{ parameters.namespace | lower }}
