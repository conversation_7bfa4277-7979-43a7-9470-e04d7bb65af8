# Backstage Templates

This repository contains Backstage scaffolder templates for deploying infrastructure components and applications using GitOps practices.

## Overview

These templates provide standardized deployment patterns for various technologies including databases, message queues, web servers, and applications. Each template follows GitOps principles, automatically configuring GitLab repositories and ArgoCD applications for continuous deployment.

## Available Templates

### Infrastructure
- **Database Solutions**: PostgreSQL clusters, FerretDB, KeyDB
- **Message Queues**: Apache Kafka clusters and topics
- **Web Infrastructure**: Nginx Ingress Controllers
- **Application Platforms**: .NET web applications

### Common Features
- Kubernetes manifest generation
- GitLab repository integration
- ArgoCD application creation
- Backstage component registration
- Environment-specific configurations (dev/prod)

## Getting Started

1. **Access Backstage**: Navigate to your Backstage instance
2. **Create Component**: Use the "Create Component" feature
3. **Select Template**: Choose the appropriate template for your needs
4. **Configure Parameters**: Fill in the required configuration parameters
5. **Deploy**: The template will handle GitLab commits and ArgoCD setup

## Template Structure

Each template generates:
- Kubernetes manifests for deployment
- Backstage component descriptors
- ArgoCD application configurations
- GitLab repository integration

## Repository Integration

Templates work with these GitLab repositories:
- **App Configurations**: `backstage-apps` (Kubernetes manifests and configs)
- **ArgoCD Applications**: `backstage-argocd-apps` (ArgoCD application definitions)

## Support

For questions about specific templates, refer to the individual template documentation. For general issues with the template system, contact your platform team.
